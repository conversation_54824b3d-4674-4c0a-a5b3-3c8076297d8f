{
    {{#composerPackageName}}
    "name": "{{{.}}}",
    {{/composerPackageName}}
    {{^composerPackageName}}
    "name": "openapi/php-laravel",
    {{/composerPackageName}}
    {{#artifactVersion}}
    "version": "{{.}}",
    {{/artifactVersion}}
    "description": "{{description}}",
    "keywords": [
        "openapi",
        "php",
        "sdk",
        "api"
    ],
    "homepage": "{{{artifactUrl}}}",
    "license": "{{{licenseName}}}",
    "authors": [
        {
            "name": "{{{developerOrganization}}}",
            "homepage": "{{{developerOrganizationUrl}}}"
        }
    ],
    "require": {
        "php": "^8.1",
        "laravel/framework": "^10.0",
        "crell/serde": "^1.0"
    },
    "require-dev": {
        "phpunit/phpunit": "^9.5"
    },
    "autoload": {
        "psr-4": {
            "{{escapedInvokerPackage}}\\" : "{{relativeSrcBasePath}}"
        }
    }
}
