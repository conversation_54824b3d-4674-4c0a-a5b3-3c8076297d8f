$validator = Validator::make(
            array_merge(
                [
                    {{#pathParams}}'{{paramName}}' => ${{paramName}},{{/pathParams}}
                ],
                $request->all(),
            ),
            [
            {{#allParams}}
            {{^bodyParam}}
                '{{paramName}}' => [
                {{#isFile}}
                    'file',
                {{/isFile}}
                {{#required}}
                    'required',
                {{/required}}
                {{#minLength}}
                    'min:{{minLength}}',
                {{/minLength}}
                {{#maxLength}}
                    'max:{{maxLength}}',
                {{/maxLength}}
                {{#pattern}}
                    'regex:{{{pattern}}}',
                {{/pattern}}
                {{#minimum}}
                {{#exclusiveMinimum}}
                    'gt:{{minimum}}',
                {{/exclusiveMinimum}}
                {{^exclusiveMinimum}}
                    'gte:{{minimum}}',
                {{/exclusiveMinimum}}
                {{/minimum}}
                {{#maximum}}
                {{#exclusiveMaximum}}
                    'lt:{{maximum}}',
                {{/exclusiveMaximum}}
                {{^exclusiveMaximum}}
                    'lte:{{maximum}}',
                {{/exclusiveMaximum}}
                {{/maximum}}
                {{#minItems}}
                    'min:{{minItems}}',
                {{/minItems}}
                {{#maxItems}}
                    'max:{{maxItems}}',
                {{/maxItems}}
                {{#isString}}
                    'string',
                {{/isString}}
                {{#isInteger}}
                    'integer',
                {{/isInteger}}
                {{#isLong}}
                    'integer',
                {{/isLong}}
                {{#isNumeric}}
                    'numeric',
                {{/isNumeric}}
                {{#isBoolean}}
                    'boolean',
                {{/isBoolean}}
                {{#isArray}}
                    'array',
                {{/isArray}}
                {{#isDateTime}}
                    'date',
                {{/isDateTime}}
                ],
            {{/bodyParam}}
            {{/allParams}}
            ],
        );

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid input'], 400);
        }