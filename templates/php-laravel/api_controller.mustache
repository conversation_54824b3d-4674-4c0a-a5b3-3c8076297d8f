{{>php_file_header}}

namespace {{controllerPackage}};

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
{{#operations}}
{{#operation}}
{{#allParams}}
{{#isBodyParam}}
use App\Http\Requests\{{operationIdCamelCase}}Request;
{{/isBodyParam}}
{{/allParams}}
{{/operation}}
{{/operations}}
{{#operations}}
{{#operation}}
use App\Http\Resources\{{operationIdCamelCase}}Resource;
{{/operation}}
{{/operations}}
{{#operations}}
{{#operation}}
{{#allParams}}
{{#isBodyParam}}
use App\Services\Dto\{{operationIdCamelCase}}Dto;
{{/isBodyParam}}
{{/allParams}}
{{/operation}}
{{/operations}}
use {{apiPackage}}\{{classname}};

{{#operations}}
/**
 * Class {{controllerName}}
 */
class {{controllerName}} extends Controller
{
    public function __construct(
        private readonly {{classname}} $service,
    ) {
    }

    {{#operation}}
    /**
     * {{{summary}}}
     {{#isDeprecated}}
     *
     * @deprecated
     {{/isDeprecated}}
     {{#hasExceptions}}
     * @throws \Exception
     {{/hasExceptions}}
     */
    public function {{operationId}}({{#allParams}}{{#isBodyParam}}{{operationIdCamelCase}}Request $request{{/isBodyParam}}{{#isPathParam}}{{dataType}} ${{paramName}}{{/isPathParam}}{{#isHeaderParam}}{{dataType}} ${{paramName}}{{/isHeaderParam}}{{^-last}}, {{/-last}}{{/allParams}}){{#returnType}}: {{#isArray}}AnonymousResourceCollection{{/isArray}}{{^isArray}}{{#isMap}}JsonResponse{{/isMap}}{{^isMap}}{{operationIdCamelCase}}Resource{{/isMap}}{{/isArray}}{{/returnType}}
    {
        {{#allParams}}
        {{#isBodyParam}}
        /** @var {{operationIdCamelCase}}Dto $dto */
        $dto = {{operationIdCamelCase}}Dto::fromArray($request->validated());

        {{/isBodyParam}}
        {{/allParams}}
        {{#returnType}}
        {{#isArray}}
        return {{operationIdCamelCase}}Resource::collection($this->service->{{operationId}}({{#allParams}}{{#isBodyParam}}$dto{{/isBodyParam}}{{#isPathParam}}${{paramName}}{{/isPathParam}}{{#isHeaderParam}}${{paramName}}{{/isHeaderParam}}{{^-last}}, {{/-last}}{{/allParams}}));
        {{/isArray}}
        {{^isArray}}
        {{#isMap}}
        $result = $this->service->{{operationId}}({{#allParams}}{{#isBodyParam}}$dto{{/isBodyParam}}{{#isPathParam}}${{paramName}}{{/isPathParam}}{{#isHeaderParam}}${{paramName}}{{/isHeaderParam}}{{^-last}}, {{/-last}}{{/allParams}});

        return $this->success($result);
        {{/isMap}}
        {{^isMap}}
        return {{operationIdCamelCase}}Resource::make($this->service->{{operationId}}({{#allParams}}{{#isBodyParam}}$dto{{/isBodyParam}}{{#isPathParam}}${{paramName}}{{/isPathParam}}{{#isHeaderParam}}${{paramName}}{{/isHeaderParam}}{{^-last}}, {{/-last}}{{/allParams}}));
        {{/isMap}}
        {{/isArray}}
        {{/returnType}}
        {{^returnType}}
        $this->service->{{operationId}}({{#allParams}}{{#isBodyParam}}$dto{{/isBodyParam}}{{#isPathParam}}${{paramName}}{{/isPathParam}}{{#isHeaderParam}}${{paramName}}{{/isHeaderParam}}{{^-last}}, {{/-last}}{{/allParams}});
        {{/returnType}}
    }

    {{/operation}}
}
{{/operations}}
