{{>php_file_header}}

namespace {{controllerPackage}};

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
{{#operations}}
{{#operation}}
{{#allParams}}
{{#isBodyParam}}
use App\Http\Requests\{{operationIdCamelCase}}Request;
{{/isBodyParam}}
{{/allParams}}
{{/operation}}
{{/operations}}
{{#operations}}
{{#operation}}
use App\Http\Resources\{{operationIdCamelCase}}Resource;
{{/operation}}
{{/operations}}
{{#operations}}
{{#operation}}
{{#allParams}}
{{#isBodyParam}}
use App\Services\Dto\{{operationIdCamelCase}}Dto;
{{/isBodyParam}}
{{/allParams}}
{{/operation}}
{{/operations}}
use {{apiPackage}}\{{classname}};

{{#operations}}
/**
 * {{#version}}The version of the OpenAPI document: {{{.}}}{{/version}}
 *
 * Class {{controllerName}}
 */
class {{controllerName}} extends Controller
{
    public function __construct(
        private readonly {{classname}} $service,
    ) {
    }

    {{#operation}}
    /**
     * {{{summary}}}
     {{#isDeprecated}}
     *
     * @deprecated
     {{/isDeprecated}}
     {{#hasExceptions}}
     * @throws \Exception
     {{/hasExceptions}}
     */
    public function {{operationId}}({{#allParams}}{{^isHeaderParam}}{{#isBodyParam}}{{operationIdCamelCase}}Request $request{{/isBodyParam}}{{#isPathParam}}{{dataType}} ${{paramName}}{{/isPathParam}}{{^-last}}, {{/-last}}{{/isHeaderParam}}{{/allParams}}){{#returnType}}: {{#isArray}}AnonymousResourceCollection{{/isArray}}{{^isArray}}{{#isMap}}JsonResponse{{/isMap}}{{^isMap}}{{operationIdCamelCase}}Resource{{/isMap}}{{/isArray}}{{/returnType}}
    {
        {{#allParams}}
        {{#isBodyParam}}
        /** @var {{operationIdCamelCase}}Dto $dto */
        $dto = {{operationIdCamelCase}}Dto::fromArray($request->validated());

        {{/isBodyParam}}
        {{/allParams}}
        {{#returnType}}
        {{#isArray}}
        return {{operationIdCamelCase}}Resource::collection($this->service->{{operationId}}({{#allParams}}{{^isHeaderParam}}{{#isBodyParam}}$dto{{/isBodyParam}}{{#isPathParam}}${{paramName}}{{/isPathParam}}{{^-last}}, {{/-last}}{{/isHeaderParam}}{{/allParams}}));
        {{/isArray}}
        {{^isArray}}
        {{#isMap}}
        $result = $this->service->{{operationId}}({{#allParams}}{{^isHeaderParam}}{{#isBodyParam}}$dto{{/isBodyParam}}{{#isPathParam}}${{paramName}}{{/isPathParam}}{{^-last}}, {{/-last}}{{/isHeaderParam}}{{/allParams}});

        return $this->success($result);
        {{/isMap}}
        {{^isMap}}
        return {{operationIdCamelCase}}Resource::make($this->service->{{operationId}}({{#allParams}}{{^isHeaderParam}}{{#isBodyParam}}$dto{{/isBodyParam}}{{#isPathParam}}${{paramName}}{{/isPathParam}}{{^-last}}, {{/-last}}{{/isHeaderParam}}{{/allParams}}));
        {{/isMap}}
        {{/isArray}}
        {{/returnType}}
        {{^returnType}}
        $this->service->{{operationId}}({{#allParams}}{{^isHeaderParam}}{{#isBodyParam}}$dto{{/isBodyParam}}{{#isPathParam}}${{paramName}}{{/isPathParam}}{{^-last}}, {{/-last}}{{/isHeaderParam}}{{/allParams}});
        {{/returnType}}
    }

    {{/operation}}
}
{{/operations}}
