{{>php_file_header}}

namespace App\Http\Requests;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

/**
 * Class {{operationIdCamelCase}}Request
 *
{{#allParams}}
{{#isBodyParam}}
{{#vars}}
 * @property {{#isNullable}}null|{{/isNullable}}{{#isArray}}array{{/isArray}}{{#isString}}string{{/isString}}{{#isInteger}}int{{/isInteger}}{{#isLong}}int{{/isLong}}{{#isFloat}}float{{/isFloat}}{{#isDouble}}float{{/isDouble}}{{#isNumber}}float{{/isNumber}}{{#isBoolean}}bool{{/isBoolean}}{{#isObject}}array{{/isObject}} ${{name}}
{{/vars}}
{{/isBodyParam}}
{{/allParams}}
 */
class {{operationIdCamelCase}}Request extends BaseRequest
{
    /**
     * @return array<string, array<string>>
     */
    public function rules(): array
    {
        return [
{{#allParams}}
{{#isBodyParam}}
{{#vars}}
            '{{name}}' => [{{#required}}'required'{{/required}}{{^required}}'nullable'{{/required}}{{#isString}}, 'string'{{/isString}}{{#isInteger}}, 'integer'{{/isInteger}}{{#isLong}}, 'integer'{{/isLong}}{{#isFloat}}, 'numeric'{{/isFloat}}{{#isDouble}}, 'numeric'{{/isDouble}}{{#isNumber}}, 'numeric'{{/isNumber}}{{#isBoolean}}, 'boolean'{{/isBoolean}}{{#isArray}}, 'array'{{/isArray}}{{#isObject}}, 'array'{{/isObject}}{{#hasValidation}}{{#minLength}}, 'min:{{minLength}}'{{/minLength}}{{#maxLength}}, 'max:{{maxLength}}'{{/maxLength}}{{#minimum}}, 'min:{{minimum}}'{{/minimum}}{{#maximum}}, 'max:{{maximum}}'{{/maximum}}{{#pattern}}, 'regex:{{pattern}}'{{/pattern}}{{/hasValidation}}{{#hasEnum}}, Rule::in([{{#allowableValues}}{{#enumValues}}'{{.}}'{{^-last}}, {{/-last}}{{/enumValues}}{{/allowableValues}}]){{/hasEnum}}],
{{/vars}}
{{/isBodyParam}}
{{/allParams}}
        ];
    }
}
