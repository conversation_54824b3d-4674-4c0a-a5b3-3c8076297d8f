{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "deversin/signature": "1.3.1", "guzzlehttp/guzzle": "^7.2", "hedii/laravel-gelf-logger": "^8.0", "laravel/framework": "^10.10", "laravel/tinker": "^2.8", "nextapps/laravel-swagger-ui": "^0.9.2", "spatie/laravel-responsecache": "^7.4", "ext-pdo": "*"}, "require-dev": {"fakerphp/faker": "^1.9.1", "friendsofphp/php-cs-fixer": "^3.75", "laravel/pint": "^1.14", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "repositories": [{"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/logger.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/white-label/wl-crypto-signature.git"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}