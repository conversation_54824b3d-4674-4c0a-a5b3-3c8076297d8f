#!/bin/bash

# Remove previously generated code
sudo rm -fr ./.generated-code/

# Generate code using OpenAPI generator
docker run --rm \
    -v $(pwd):/local \
    openapitools/openapi-generator-cli generate \
    -i /local/resources/swagger/openapi.json \
    -g php-laravel \
    -o /local/generated-code \
    -t /local/templates/php-laravel \
    --additional-properties=modelPackage=Models \
    --skip-validate-spec \
    --global-property=metadata=true

# Set proper permissions
chmod 0755 -R ./generated-code/
