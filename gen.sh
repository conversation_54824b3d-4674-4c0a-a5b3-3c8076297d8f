#!/bin/bash

# Remove previously generated code
rm -fr ./.generated-code/

# Generate code using OpenAPI generator
docker run --rm \
    -v $(pwd):/local \
    openapitools/openapi-generator-cli generate \
    -i /local/resources/swagger/openapi.json \
    -g php-laravel \
    -o /local/generated-code \
    -t /local/templates/php-laravel \
    -c /local/openapi-config.json \
    --skip-validate-spec

# Set proper permissions
chmod 0755 -R ./generated-code/

# Fix generated code issues
echo "Running post-processor to fix generated code..."
php fix-generated-code.php ./generated-code
php fix-controller-params.php
