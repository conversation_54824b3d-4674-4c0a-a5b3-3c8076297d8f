#!/bin/bash

# <PERSON>ript to convert generated Serde models to Laravel Form Request classes

# Configuration
MODELS_DIR="generated-code/Models"
REQUESTS_DIR="generated-code/Http/Requests"
NAMESPACE="App\\Http\\Requests"

# Create requests directory
mkdir -p "$REQUESTS_DIR"

# Process each *Request.php model
for model_file in "$MODELS_DIR"/*Request.php; do
    if [ -f "$model_file" ]; then
        # Extract class name from filename
        filename=$(basename "$model_file")
        classname="${filename%.php}"
        
        echo "Converting $classname to Laravel Form Request..."
        
        # Generate Laravel Form Request
        cat > "$REQUESTS_DIR/$classname.php" << EOF
<?php

declare(strict_types=1);

namespace $NAMESPACE;

use App\\Http\\Requests\\BaseRequest;
use Illuminate\\Validation\\Rule;

/**
 * Class $classname
 */
class $classname extends BaseRequest
{
    /**
     * @return array<string, array<string>>
     */
    public function rules(): array
    {
        return [
            // TODO: Add validation rules based on OpenAPI schema
        ];
    }
}
EOF
        
        echo "Generated: $REQUESTS_DIR/$classname.php"
    fi
done

echo "Form Request generation completed!"
