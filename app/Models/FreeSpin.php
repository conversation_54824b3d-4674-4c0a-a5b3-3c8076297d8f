<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Database\Eloquent\Builder as BuilderContract;

/**
 * Class FreeSpin
 *
 * @property int $id
 * @property int $slot_id
 * @property null|string $slot_external_id
 * @property string $type
 * @property string $supplier
 * @property string $aggregator
 * @property int $provider_id
 * @property int $bonus_id
 * @property string $name
 * @property string $title
 * @property int $count
 * @property null|int $bet
 * @property string $bet_id
 * @property string $currency
 * @property string $status
 * @property bool $is_active
 * @property int $author_id
 * @property float $denomination
 * @property null|int $duration
 * @property bool $in_process
 * @property string $data
 * @property int $updated_by_author_id
 * @property DateTimeInterface $start_at
 * @property DateTimeInterface $expired_at
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 * @property-read Collection<int, FreeSpinBound> $bounds
 * @property-read null|Bonus $bonus
 *  @property null|array<string, string> $options
 * @property null|string $author_email
 */
class FreeSpin extends Model
{
    use SoftDeletes;

    /**
     * @var string
     */
    protected $table = 'free_spins';

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    protected $fillable = [
        'supplier',
        'aggregator',
        'slot_id',
        'slot_external_id',
        'provider_id',
        'bonus_id',
        'type',
        'name',
        'title',
        'count',
        'bet',
        'bet_id',
        'denomination',
        'currency',
        'status',
        'is_active',
        'author_id',
        'updated_by_author_id',
        'start_at',
        'expired_at',
        'data',
        'in_process',
        'duration',
        'options',
        'author_email',
    ];

    /**
     * @var string[]
     */
    protected $casts = [
        'denomination' => 'float',
        'bonus_id' => 'integer',
        'options' => 'array',
    ];

    public const MANUAL_TYPE = 'manual';
    public const FOR_DEP_TYPE = 'for dep';

    public function bounds(): BuilderContract|HasMany
    {
        return $this->hasMany(FreeSpinBound::class, 'free_spin_id', 'id');
    }

    public function bonus(): BuilderContract|HasOne
    {
        return $this->hasOne(Bonus::class, 'id', 'bonus_id');
    }
}
