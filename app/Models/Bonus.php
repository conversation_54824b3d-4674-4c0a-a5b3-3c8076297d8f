<?php

namespace App\Models;

use App\Enums\BonusTypeEnum;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use Illuminate\Contracts\Database\Eloquent\Builder as BuilderContract;

/**
 * Class Bonus
 *
 * @property int $id
 * @property null|string $author_email
 * @property int $client_id
 * @property string $type
 * @property string $name
 * @property array<int> $bonuses
 * @property array<int> $max_transfers
 * @property array<int> $deposit_factors
 * @property string $bet
 * @property string $currency
 * @property string $bonus_name
 * @property string $description
 * @property string $condition
 * @property int $duration
 * @property string $image
 * @property int $min_deposit
 * @property float $min_factor
 * @property int $min_bet
 * @property int $uses
 * @property float $wager
 * @property bool $active
 * @property bool $casino
 * @property bool $bets
 * @property bool $crash
 * @property int $total_transferred
 * @property int $transfers
 * @property int $total_uses
 * @property bool $is_external
 * @property bool $is_promo
 * @property int $weight
 * @property bool $is_welcome_group
 * @property bool $is_onetime_group
 * @property bool $is_welcome_4_steps
 * @property bool $is_no_dep
 * @property bool $is_deposit
 * @property bool $is_free_spin_for_deposit
 * @property bool $is_organic
 * @property bool $genre_id
 * @property array<string, null|string|int|array<int>> $data
 * @property null|Carbon $active_from
 * @property null|Carbon $active_til
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property string $uuid
 * @property null|int $max_real_balance
 * @property Collection<int, BonusTriggerSession> $triggerSessions
 * @property-read Collection<int, BonusSlotProvider> $bonusSlotProviders
 * @property-read Collection<int, BonusSlot> $bonusSlots
 * @property-read null|FreeSpin $freespin
 *
 * @method static findOrFail(mixed $value)
 * @method static Builder|Bonus withoutFreeSpinBonusType()
 * @method static Builder|Bonus isWelcomeGroupType()
 * @method static Builder|Bonus isNotWelcomeGroupType()
 */
class Bonus extends Model
{
    /**
     * @var string
     */
    protected $table = 'bonuses';

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    public $timestamps = true;

    /**
     * @var array|string[]
     */
    protected array $dates = [
        'active_from',
        'active_til',
    ];

    /**
     * @var array<string, float|int|string>
     */
    protected $attributes = [
        'wager' => 10.00,
        'data' => '{}',
        'uses' => 0,
        'deposit_factors' => '{}',
        'total_transferred' => 0,
        'total_uses' => 0,
    ];

    /**
     * @var string[]
     */
    protected $casts = [
        'bonuses' => 'array',
        'max_transfers' => 'array',
        'deposit_factors' => 'array',
        'active_from' => 'datetime',
        'active_til' => 'datetime',
        'bonus_name' => 'array',
        'description' => 'array',
        'condition' => 'array',
        'data' => 'array',
        'wager' => 'float',
        'min_factor' => 'float',
        'active' => 'bool',
        'casino' => 'bool',
        'bets' => 'bool',
        'crash' => 'bool',
        'is_organic' => 'bool',
        'is_external' => 'bool',
        'is_promo' => 'bool',
    ];

    protected $fillable = [
        'author_email',
        'image',
        'casino',
        'bets',
        'active',
        'min_bet',
        'client_id',
        'deposit_factors',
        'type',
        'wager',
        'max_transfers',
        'bonus_name',
        'description',
        'condition',
        'min_deposit',
        'bonuses',
        'currency',
        'min_factor',
        'name',
        'total_transferred',
        'crash',
        'uses',
        'transfers',
        'total_uses',
        'is_external',
        'is_promo',
        'active_from',
        'active_til',
        'duration',
        'data',
        'weight',
        'max_real_balance',
        'is_organic',
        'genre_id',
        'updated_at',
    ];

    public static function boot(): void
    {
        parent::boot();

        static::creating(static function ($banner): void {
            $banner->uuid = (string)Str::uuid();
        });
    }

    public function bonusSlotProviders(): BuilderContract|HasMany
    {
        return $this->hasMany(BonusSlotProvider::class, 'bonus_id', 'id');
    }

    public function bonusSlots(): BuilderContract|HasMany
    {
        return $this->hasMany(BonusSlot::class, 'bonus_id', 'id');
    }

    public function freespin(): BelongsTo
    {
        return $this->belongsTo(FreeSpin::class, 'id', 'bonus_id');
    }

    public function triggerSessions(): BuilderContract|HasMany
    {
        return $this->hasMany(BonusTriggerSession::class, 'bonus_id', 'id');
    }

    public function activeTriggerSessions(): BuilderContract|HasMany
    {
        return $this->triggerSessions()->where('end_at', '>', Carbon::now());
    }

    public function balances(): BuilderContract|HasMany
    {
        return $this->hasMany(BonusBalance::class, 'bonus_id', 'id');
    }

    protected function serializeDate(DateTimeInterface $date): int|string
    {
        return $date->getTimestamp();
    }

    public function scopeWithoutFreeSpinBonusType(Builder $query): Builder
    {
        return $query->where('type', '!=', BonusTypeEnum::FREESPIN_BONUS->value);
    }

    public function scopeIsWelcomeGroupType(Builder $query): Builder|QueryBuilder
    {
        return $query->whereIn('type', [BonusTypeEnum::WELCOME_4_STEPS->value, BonusTypeEnum::WELCOME->value]);
    }

    public function scopeIsNotWelcomeGroupType(Builder $query): Builder|QueryBuilder
    {
        return $query->whereNotIn('type', [BonusTypeEnum::WELCOME_4_STEPS->value, BonusTypeEnum::WELCOME->value]);
    }

    public function getCurrentTriggerSession(): ?BonusTriggerSession
    {
        $triggerSessions = $this->triggerSessions;
        $now = Carbon::now();

        /** @var BonusTriggerSession */
        return $triggerSessions->where('start_at', '<', $now)
            ->where('end_at', '>', $now)
            ->first();
    }

    public function getNextTriggerSession(): ?BonusTriggerSession
    {
        $triggerSessions = $this->triggerSessions;
        $now = Carbon::now();

        /** @var BonusTriggerSession */
        return $triggerSessions->sortBy('start_at')
            ->where('start_at', '>', $now)
            ->first();
    }

    public function getPreviousTriggerSession(): ?BonusTriggerSession
    {
        $triggerSessions = $this->triggerSessions;

        $now = Carbon::now();

        /** @var BonusTriggerSession */
        return $triggerSessions->sortBy('start_at')
            ->where('end_at', '<', $now)
            ->first();
    }

    public function isValidTimeBonus(): bool
    {
        return (!$this->active_from || time() > $this->active_from->getTimestamp())
            && (!$this->active_til || time() < $this->active_til->getTimestamp());
    }

    public function bonusPlayers(): HasMany
    {
        return $this->hasMany(BonusPlayer::class);
    }

    protected function isWelcomeGroup(): Attribute
    {
        return Attribute::make(
            get: fn() => in_array($this->type, [
                BonusTypeEnum::WELCOME->value,
                BonusTypeEnum::WELCOME_4_STEPS->value,
            ], true)
        );
    }

    public function isOnetimeGroup(): Attribute
    {
        return Attribute::make(
            get: fn() => in_array($this->type, [
                BonusTypeEnum::ONETIME->value,
                BonusTypeEnum::FREE_MONEY->value,
                BonusTypeEnum::FREE_BET->value,
            ], true)
        );
    }

    protected function isWelcome4Steps(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->type === BonusTypeEnum::WELCOME_4_STEPS->value
        );
    }

    public function isNoDep(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->type === BonusTypeEnum::NO_DEP->value
        );
    }

    public function isDeposit(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->type === BonusTypeEnum::DEPOSIT->value
        );
    }

    public function isFreeSpinForDeposit(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->type === BonusTypeEnum::FREE_SPINS_FOR_DEPOSIT->value
        );
    }

    public function isUsedGenre(): bool
    {
        return $this->genre_id !== null;
    }
}
