<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $free_spin_id
 * @property null|string $author_email
 * @property string $type
 * @property null|string $diff
 * @property array $data
 * @property Carbon $created_at
 */
class FreeSpinHistoryLog extends Model
{
    protected $connection = 'mysql.bonus';

    protected $table = 'freespin_history_logs';

    protected $casts = [
        'data' => 'array',
    ];

    protected $fillable = [
        'free_spin_id',
        'author_email',
        'type',
        'data',
    ];
}
