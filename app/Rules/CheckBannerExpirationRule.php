<?php

namespace App\Rules;

use App\Services\BannerService;
use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckBannerExpirationRule implements ValidationRule
{
    public function __construct(
        private readonly ?int $endAt
    ) {
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        /** @var BannerService $bannerService */
        $bannerService = app(BannerService::class);
        $banner = $bannerService->getBannerById($value);

        $endAt = Carbon::createFromTimestamp($this->endAt);
        if ($banner->isExpired() && $endAt <= now()) {
            $fail('Banner has expired');
        }
    }
}
