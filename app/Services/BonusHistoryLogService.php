<?php

namespace App\Services;

use App\Http\Resources\BonusCrudResource;
use App\Models\Bonus;
use App\Models\BonusHistoryLog;
use App\Repositories\BonusHistoryLogRepository;
use App\Services\Dto\BonusHistoryLogDTO;
use App\Services\Dto\SlotDto;
use App\Services\Dto\SlotProviderDto;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * Class BonusHistoryLogService
 */
class BonusHistoryLogService
{
    private const EXCLUDED_FIELDS = [
        'id', 'author_email', 'client_id', 'created_at', 'updated_at', 'max_bonus', 'bonus_data',
        'uses', 'is_promo', 'is_no_dep', 'transfers', 'is_deposit', 'is_welcome', 'total_uses',
        'is_external', 'total_transferred', 'is_free_spin_for_deposit',
    ];

    public function __construct(
        private readonly BonusHistoryLogRepository $bonusRepository,
        private readonly SlotService $slotService,
        private readonly SlotProviderService $slotProviderService,
    ) {
    }

    public function list(BonusHistoryLogDTO $dto): LengthAwarePaginator
    {
        return $this->bonusRepository->getLogsByBonusId($dto);
    }

    public function createLog(Bonus $bonus, string $type = 'create', bool $withDiff = true): void
    {
        $bonusData = $this->getBonusData($bonus);
        $diff = $withDiff ? $this->getDiff(array_diff(array_keys($bonusData), self::EXCLUDED_FIELDS), [], $bonusData) : [];

        $this->createBonusHistoryLog($bonus, $type, $bonusData, $diff);
    }

    public function updateLog(Bonus $bonus): void
    {
        $bonusData = $this->getBonusData($bonus);
        $prevBonusHistoryLog = $this->getLastBonusHistoryLog($bonus->id);

        if (!$prevBonusHistoryLog) {
            return;
        }

        $diff = $this->getDiff(
            array_diff(array_keys($bonusData), self::EXCLUDED_FIELDS),
            $prevBonusHistoryLog->data['bonus'] ?? [],
            $bonusData
        );
        if ($diff) {
            $this->updateBonusHistoryLog($prevBonusHistoryLog, $diff, $bonus->author_email);
        } else {
            $prevBonusHistoryLog->delete();
        }
    }

    /**
     * @param string[] $fields
     * @param array<string, mixed> $prev
     * @param array<string, mixed> $current
     *
     * @return array<string, mixed>
     */
    public function getDiff(array $fields, array $prev, array $current): array
    {
        $excludeFields = ['bonus_name', 'description', 'condition'];
        $diff = [];

        foreach ($fields as $field) {
            if (isset($current[$field]) || isset($prev[$field])) {
                if (($prev[$field] ?? null) != ($current[$field] ?? null) && !in_array($field, $excludeFields, true)) {
                    if ($field === 'freespin') {
                        $diff += $this->handleFieldDiff($field, $prev, $current);
                    } else {
                        $handledDiff = $this->handleFieldDiff($field, $prev, $current);
                        if ($handledDiff) {
                            $diff[$field] = $handledDiff;
                        }
                    }
                } elseif (in_array($field, $excludeFields, true)) {
                    $diffExcluded = $this->getDiff(
                        array_keys(array_merge($current[$field] ?? [], $prev[$field] ?? [])),
                        $prev[$field] ?? [],
                        $current[$field] ?? []
                    );
                    if ($diffExcluded) {
                        $diff[$field] = $diffExcluded;
                    }
                }
            }
        }

        if (isset($diff['slot_providers']) && !isset($diff['slots'])) {
            $diff['slots'] = [
                'current' => [],
                'previous' => [],
            ];
        }

        return $diff;
    }

    /**
     * @param array<string, mixed> $prev
     * @param array<string, mixed> $current
     *
     * @return array<string, mixed>
     */
    private function handleFieldDiff(string $field, array $prev, array $current): array
    {
        switch ($field) {
            case 'slots':
                return $this->isDiffSlots($prev[$field] ?? [], $current[$field] ?? []);
            case 'slot_providers':
                return $this->isDiffSlotProviders($prev[$field] ?? [], $current[$field] ?? []);
            case 'trigger_sessions':
                if ($this->isDiffArray(
                    isset($prev[$field]) ? (is_array($prev[$field]) ? $prev[$field] : $prev[$field]->toArray(request())) : [],
                    isset($current[$field]) ? (is_array($current[$field]) ? $current[$field] : $current[$field]->toArray(request())) : [],
                )) {
                    return [
                        'previous' => $prev[$field] ?? [],
                        'current' => $current[$field] ?? [],
                    ];
                }

                break;
            case 'freespin':
                return $this->getFreeSpinDiff(
                    isset($prev[$field]) ? (is_array($prev[$field]) ? $prev[$field] : $prev[$field]->toArray(request())) : [],
                    isset($current[$field]) ? (is_array($current[$field]) ? $current[$field] : $current[$field]->toArray(request())) : []
                );
            default:
                return [
                    'previous' => $prev[$field] ?? null,
                    'current' => $current[$field] ?? null,
                ];
        }

        return [];
    }

    /**
     * @param array<string, null|int|string> $prev
     * @param array<string, null|int|string> $current
     *
     * @return array<string, array<string, null|int|string|bool>>
     */
    public function getFreeSpinDiff(array $prev, array $current): array
    {
        $fields = ['count', 'bet', 'denomination'];
        $diff = [];

        foreach ($fields as $field) {
            if (($prev[$field] ?? null) != ($current[$field] ?? null)) {
                $diff[$field] = [
                    'previous' => $prev[$field] ?? null,
                    'current' => $current[$field] ?? null,
                ];
            }
        }

        if (isset($prev['slot_id']) || isset($current['slot_id'])) {
            $diffSlots = $this->isDiffSlots(
                isset($prev['slot_id']) ? [$prev['slot_id']] : [],
                isset($current['slot_id']) ? [$current['slot_id']] : []
            );

            if ($diffSlots) {
                $diff['free_spin_slots'] = $diffSlots;
            }
        }

        if (isset($prev['provider_id']) || isset($current['provider_id'])) {
            $diffSlotProviders = $this->isDiffSlotProviders(
                isset($prev['provider_id']) ? [$prev['provider_id']] : [],
                isset($current['provider_id']) ? [$current['provider_id']] : []
            );

            if ($diffSlotProviders) {
                $diff['free_spin_providers'] = $diffSlotProviders;
            }
        }

        if (count($prev) > 0 && count($current) === 0) {
            $diff['bonus_balance'] = ['previous' => true, 'current' => false];
        }

        if (count($prev) === 0 && count($current) > 0) {
            $diff['bonus_balance'] = ['previous' => false, 'current' => true];
        }

        return $diff;
    }

    /**
     * @param array<int, array{id: int}> $arr1
     * @param array<int, array{id: int}> $arr2
     */
    public function isDiffArray(array $arr1, array $arr2): bool
    {
        $ids1 = collect($arr1)->pluck('id')->toArray();
        $ids2 = collect($arr2)->pluck('id')->toArray();

        return count(array_diff($ids1, $ids2)) > 0 || count(array_diff($ids2, $ids1)) > 0;
    }

    /**
     * @param array<int, int> $prev
     * @param array<int, int> $current
     *
     * @return array<string, list<SlotDto>>
     */
    public function isDiffSlots(array $prev, array $current): array
    {
        $diffPrev = array_diff($prev, $current);
        $diffCurrent = array_diff($current, $prev);

        if (count($diffPrev) === 0 && count($diffCurrent) === 0) {
            return [];
        }

        $previous = [];
        if (count($diffPrev) > 0) {
            $previous = $this->formatSlots($this->slotService->getSlotListByIds($diffPrev));
        }
        $current = [];
        if (count($diffCurrent) > 0) {
            $current = $this->formatSlots($this->slotService->getSlotListByIds($diffCurrent));
        }

        return ['previous' => $previous, 'current' => $current];
    }

    /**
     * @param array<int, int> $prev
     * @param array<int, int> $current
     *
     * @return array<string, list<SlotProviderDto>>
     */
    public function isDiffSlotProviders(array $prev, array $current): array
    {
        $diffPrev = array_diff($prev, $current);
        $diffCurrent = array_diff($current, $prev);

        if (count($diffPrev) === 0 && count($diffCurrent) === 0) {
            return [];
        }
        $previous = [];
        if (count($diffPrev) > 0) {
            $previous = $this->formatSlotProviders($this->slotProviderService->getSlotProvidersByIds($diffPrev));
        }
        $current = [];
        if (count($diffCurrent) > 0) {
            $current = $this->formatSlotProviders($this->slotProviderService->getSlotProvidersByIds($diffCurrent));
        }

        return ['previous' => $previous, 'current' => $current];
    }

    private function formatSlots(array $slots): array
    {
        return array_map(
            static fn(SlotDto $item) => ['id' => $item->id, 'name' => $item->name],
            array_values($slots)
        );
    }

    private function formatSlotProviders(array $slotProviders): array
    {
        return array_map(
            static fn(SlotProviderDto $item) => ['id' => $item->id, 'name' => $item->name],
            array_values($slotProviders)
        );
    }

    /**
     * @return array<string, mixed>
     */
    private function getBonusData(Bonus $bonus): array
    {
        $data = BonusCrudResource::make($bonus)->toArray(request());

        if ($data['slot_providers'] ?? null) {
            $data['slot_providers'] = $bonus->bonusSlotProviders->pluck('slot_provider_id')
                ->flatten()->filter()->unique()->toArray();
        }

        if ($data['slots'] ?? null) {
            $data['slots'] = $bonus->bonusSlots->pluck('slot_id')
                ->flatten()->filter()->unique()->toArray();
        }

        return $data;
    }

    /**
     * @param array<string, mixed> $bonusData
     * @param array<string, mixed> $diff
     */
    private function createBonusHistoryLog(Bonus $bonus, string $type, array $bonusData, array $diff): void
    {
        BonusHistoryLog::on('mysql.bonus')->create([
            'type' => $type,
            'bonus_id' => $bonus->id,
            'author_email' => $bonus->author_email,
            'data' => [
                'bonus' => $bonusData,
                'diff' => $diff,
            ],
        ]);
    }

    /**
     * @param array<string, mixed> $diff
     */
    private function updateBonusHistoryLog(BonusHistoryLog $log, array $diff, ?string $email = null): void
    {
        $data = $log->data;
        $data['diff'] = $diff;
        $log->data = $data;

        if ($email) {
            $log->author_email = $email;
        }

        $log->save();
    }

    protected function getLastBonusHistoryLog(int $bonusId): ?BonusHistoryLog
    {
        /** @var BonusHistoryLog */
        return BonusHistoryLog::on('mysql.bonus')
            ->where('bonus_id', $bonusId)
            ->orderBy('id', 'desc')
            ->first();
    }
}
