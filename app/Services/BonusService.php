<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\BonusTypeEnum;
use App\Enums\SupplierEnum;
use App\Events\BonusCreateOrUpdateEvent;
use App\Events\FreeSpinCudEvent;
use App\Exceptions\ApiException;
use App\Exceptions\Exception;
use App\Exceptions\InternalLogicException;
use App\Exceptions\InvalidArgumentException;
use App\Models\Bonus;
use App\Models\BonusInfo;
use App\Models\BonusTriggerSession;
use App\Models\FreeSpin;
use App\Models\PlayerFirstBonus;
use App\Repositories\BonusRepository;
use App\Services\Dto\ActiveWelcomeBonusesDto;
use App\Services\Dto\BetbyBonusDTO;
use App\Services\Dto\BetbySearchDTO;
use App\Services\Dto\BonusApplyDTO;
use App\Services\Dto\BonusCrudDto;
use App\Services\Dto\BonusDataDTO;
use App\Services\Dto\BonusSearchDto;
use App\Services\Dto\EnabledBonusesDTO;
use App\Services\Dto\FreeSpinCrudDto;
use App\Services\Dto\IsWagerPayableDTO;
use App\Services\Dto\RegistrationPlayerBonusDTO;
use App\ValueObjects\InternalErrorCode;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as BaseCollection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Class BonusService
 */
class BonusService
{
    private const IS_GENERAL_BONUS = 'cache:is_general_bonus';
    private const IS_WAGER_PAYABLE = 'cache:is_wager_payable';

    public function __construct(
        private readonly BonusRepository $bonusRepository,
        private readonly BonusTriggerSessionService $bonusTriggerSessionService,
        private readonly PlayerService $playerService,
        private readonly BonusApplyService $bonusApplyService,
        private readonly BetbyService $betbyService,
        private readonly SlotService $slotService,
        private readonly SlotProviderService $slotProviderService,
    ) {
    }

    /**
     * @throws Exception
     */
    public function createOrUpdate(BonusCrudDto $bonusCreateDto): Bonus
    {
        $prevBonus = null;

        $bonus = DB::transaction(function () use ($bonusCreateDto, &$prevBonus) {
            $prevFreeSpin = null;
            $freeSpin = null;
            if ($bonusCreateDto->id) {
                $prevBonus = $this->bonusRepository->getActualBonusById($bonusCreateDto->id);
                $prevBonus->load(['bonusSlotProviders', 'bonusSlots', 'triggerSessions', 'freespin']);
                $prevBonus = $this->hydrateSingleBonusWithAdditionalData($prevBonus);
                $prevFreeSpin = $prevBonus->freespin;

                if ($prevFreeSpin) {
                    // This relation is used of fill bonus data in FreeSpinHistoryLogService.
                    // To able to attach bonus to free spin we need to remove relation because of circular reference.
                    $prevFreeSpin->setRelation(
                        'bonus',
                        $prevBonus->replicate()->setRelation('freespin', null)
                    );
                }
            }
            $bonus = $this->saveBonus($bonusCreateDto);
            switch ($bonusCreateDto->type) {
                case BonusTypeEnum::FREE_SPINS_FOR_DEPOSIT->value:
                    if ($bonusCreateDto->id) {
                        /** @var FreeSpinBoundService $freeSpinBoundService */
                        $freeSpinBoundService = app(FreeSpinBoundService::class);
                        if ($freeSpinBoundService->existFreeSpinBoundByBonusId($bonusCreateDto->id)) {
                            throw new InternalLogicException(InternalErrorCode::BONUS_FOR_DEPOSIT_IS_ALREADY_USED);
                        }
                    }
                    $this->syncTriggerSessions($bonus, $bonusCreateDto->triggerSessions);
                    $freeSpinCreateDto = $this->getFreeSpinDto(
                        $bonus,
                        $bonusCreateDto,
                        $bonusCreateDto->id ? 'update' : 'create'
                    );
                    /** @var FreeSpinService $freespinService */
                    $freespinService = app(FreeSpinService::class);
                    $freeSpin = $freespinService->saveFreeSpin($freeSpinCreateDto, FreeSpin::FOR_DEP_TYPE, $bonus->id);

                    break;
                case BonusTypeEnum::DEPOSIT->value:
                    $this->syncTriggerSessions($bonus, $bonusCreateDto->triggerSessions);

                    break;
            }

            if (empty($bonusCreateDto->genreId)) {
                $this->syncBonusRelatedModels($bonus, $bonusCreateDto->slotProviders, $bonusCreateDto->slots);
            } else {
                $this->syncBonusRelatedModels($bonus, null, null);
            }

            $bonus->load(['bonusSlotProviders', 'bonusSlots', 'triggerSessions', 'freespin']);

            $this->hydrateSingleBonusFreeSpinWithSlotAndProvider($bonus);
            $this->hydrateSingleBonusWithAdditionalData($bonus);

            if ($bonusCreateDto->type === BonusTypeEnum::FREE_SPINS_FOR_DEPOSIT->value) {
                $freeSpin->setRelation('bonus', $bonus);

                event(new FreeSpinCudEvent($freeSpin->id, $freeSpin, $prevFreeSpin, $freeSpin->author_email));
            }

            return $bonus;
        });

        if (!$bonus) {
            throw new Exception('Bad Request', 400);
        }

        event(new BonusCreateOrUpdateEvent($bonus, $prevBonus));

        return $bonus;
    }

    public function searchFilteredBonusesForBonusInfoPage(BonusSearchDto $bonusSearchDto): LengthAwarePaginator
    {
        $mapSortFields = [
            'from' => 'active_from',
            'to' => 'active_til',
            'max_bonuses' => 'bonuses',
        ];

        $bonusSearchDto->asc = $mapSortFields[$bonusSearchDto->asc] ?? $bonusSearchDto->asc;
        $bonusSearchDto->desc = $mapSortFields[$bonusSearchDto->desc] ?? $bonusSearchDto->desc;
        $bonusSearchDto->pagination['limit'] ??= 50;

        $bonuses = $this->bonusRepository->searchFilteredBonusesForBonusInfoPage($bonusSearchDto);
        $this->hydrateBonusesWithAdditionalData($bonuses);
        $this->hydrateBonusesFreeSpinsWithSlotAndProvider($bonuses);

        return $bonuses;
    }

    /**
     * @param BaseCollection<int, Bonus>|LengthAwarePaginator $bonuses
     *
     * @return BaseCollection<int, Bonus>|LengthAwarePaginator
     */
    private function hydrateBonusesWithAdditionalData(
        BaseCollection|LengthAwarePaginator $bonuses
    ): BaseCollection|LengthAwarePaginator {
        $this->hydrateBonusesWithSlots($bonuses);
        $this->hydrateBonusesWithSlotsProviders($bonuses);

        return $bonuses;
    }

    private function hydrateBonusesFreeSpinsWithSlotAndProvider(
        BaseCollection|LengthAwarePaginator $bonuses
    ): BaseCollection|LengthAwarePaginator {
        $freeSpinCollection = $bonuses
            ->filter(static fn(Bonus $bonus) => $bonus->freespin !== null)
            ->map(static fn(Bonus $bonus) => $bonus->freespin);

        /** @var FreeSpinService $freeSpinService */
        $freeSpinService = app(FreeSpinService::class);

        $freeSpinService->hydrateFreeSpinsWithSlot($freeSpinCollection);
        $freeSpinService->hydrateFreeSpinsWithProvider($freeSpinCollection);

        return $bonuses;
    }

    public function hydrateSingleBonusWithAdditionalData(Bonus $bonus): Bonus
    {
        return $this->hydrateBonusesWithAdditionalData(collect([$bonus]))->first();
    }

    private function hydrateSingleBonusFreeSpinWithSlotAndProvider(Bonus $bonus): Bonus
    {
        return $this->hydrateBonusesFreeSpinsWithSlotAndProvider(collect([$bonus]))->first();
    }

    /**
     * Hydrates bonuses with corresponding slots
     *
     * @param BaseCollection<int, Bonus>|LengthAwarePaginator $bonuses
     */
    private function hydrateBonusesWithSlots(
        BaseCollection|LengthAwarePaginator $bonuses
    ): void {
        $uniqueSlotsIds = $bonuses->pluck('bonusSlots.*.slot_id')->flatten()->filter()->unique()->toArray();
        $slots = $this->slotService->getSlotListByIds($uniqueSlotsIds);

        $bonuses->map(static function (Bonus $bonus) use ($slots): void {
            $bosunSlots = $bonus->bonusSlots->pluck('slot_id')->map(
                static fn(int $id) => $slots[$id] ?? null
            )->filter()->values();

            $bonus->setAttribute('slots', $bosunSlots);
        });
    }

    /**
     * Hydrates bonuses with corresponding slot-providers
     *
     * @param BaseCollection<int, Bonus>|LengthAwarePaginator $bonuses
     */
    public function hydrateBonusesWithSlotsProviders(
        BaseCollection|LengthAwarePaginator $bonuses
    ): void {
        $slotProvidersIds = $bonuses->pluck('bonusSlotProviders.*.slot_provider_id')->flatten()
            ->filter()->unique()->toArray();

        $bonusSlotProviders = $this->slotProviderService->getSlotProvidersByIds($slotProvidersIds);
        $bonuses->map(static function (Bonus $bonus) use ($bonusSlotProviders): void {
            $bonusSlotProviders = $bonus->bonusSlotProviders->pluck('slot_provider_id')->map(
                static fn(int $id) => $bonusSlotProviders[$id] ?? null
            )->filter()->values();

            $bonus->setAttribute('slotProviders', $bonusSlotProviders);
        });
    }

    public function getActiveWelcomeBonus(int $bonusId, string $currency): ?Bonus
    {
        return $this->bonusRepository->getActiveWelcomeBonus($bonusId, $currency);
    }

    /**
     * @return Collection<int, Bonus>|LengthAwarePaginator
     */
    public function activeWelcomeBonuses(ActiveWelcomeBonusesDto $dto): Collection|LengthAwarePaginator
    {
        $mapSortFields = [
            'from' => 'active_from',
            'to' => 'active_til',
            'max_bonuses' => 'bonuses',
        ];

        $dto->asc = $mapSortFields[$dto->asc] ?? $dto->asc;
        $dto->desc = $mapSortFields[$dto->desc] ?? $dto->desc;

        return $this->bonusRepository->activeWelcomeBonuses($dto);
    }

    /**
     * @return Collection<int, Bonus>|LengthAwarePaginator
     */
    public function enabledBonuses(EnabledBonusesDTO $dto): Collection|LengthAwarePaginator
    {
        $mapSortFields = [
            'from' => 'active_from',
            'to' => 'active_til',
            'max_bonuses' => 'bonuses',
        ];

        $dto->asc = $mapSortFields[$dto->asc] ?? $dto->asc;
        $dto->desc = $mapSortFields[$dto->desc] ?? $dto->desc;

        return $this->bonusRepository->enabledBonuses($dto);
    }

    public function saveBonus(BonusCrudDto $bonusCreateDto): Bonus
    {
        $bonus = $this->fillBonusData($bonusCreateDto->getDataForCreatingBonus(), $bonusCreateDto->id);
        $this->bonusRepository->save($bonus);

        return $bonus;
    }

    /**
     * @param array<string, int|string|bool> $data
     */
    public function fillBonusData(array $data, ?int $id): Bonus
    {
        $bonus = $id ? $this->bonusRepository->getBonus($id) : new Bonus();
        $bonus->fill($data);

        return $bonus;
    }

    /**
     * @throws InvalidArgumentException|Exception|ApiException
     */
    public function apply(BonusApplyDTO $dto): Bonus
    {
        $player = $this->playerService->getPlayerByUUID($dto->playerUuid);
        /** @var Bonus $bonus */
        $bonus = $this->bonusRepository->getBonusById($dto->bonusId);

        if ($bonus->is_welcome_group) {
            $this->bonusApplyService->applyWelcomeBonusForPlayer($bonus, $player);
        } elseif ($bonus->is_deposit) {
            $this->bonusApplyService->applyDepositBonusForPlayer($bonus, $player);
        } elseif ($bonus->is_free_spin_for_deposit) {
            $this->bonusApplyService->applyFreeSpinForDepositBonusForPlayer($bonus, $player);
        } elseif ($bonus->is_onetime_group) {
            $this->bonusApplyService->applyOnetimeForPlayer($bonus, $player, $dto->amount);
        } else {
            throw new Exception('Invalid argument provided for bonusId', 400);
        }

        return $this->hydrateSingleBonusWithAdditionalData($bonus);
    }

    public function createOrUpdateDefaultFreespinBonus(FreeSpinCrudDto $dto, ?int $bonusId = null): Bonus
    {
        $data = $this->preparedDataForDefaultFreespinBonus($dto);
        $bonus = $this->fillBonusData($data, $bonusId);
        $this->bonusRepository->save($bonus);

        if (null === $dto->bonusGenreId) {
            $this->syncBonusRelatedModels($bonus, $dto->bonusSlotProviders, $dto->bonusSlots);
        } else {
            $this->syncBonusRelatedModels($bonus, null, null); // Clean relations if genre is set
        }

        $bonus->load(['bonusSlotProviders', 'bonusSlots', 'triggerSessions', 'freespin']);

        return $this->hydrateSingleBonusWithAdditionalData($bonus);
    }

    public function getFreeSpinDto(Bonus $bonus, BonusCrudDto $bonusCreateDto, string $action): FreeSpinCrudDto
    {
        $id = null;
        if ($action === 'update') {
            /** @var FreeSpinService $freespinService */
            $freespinService = app(FreeSpinService::class);
            $freespin = $freespinService->getFreeSpinByBonusId($bonus->id);
            $id = $freespin->id;
        }

        if ($bonusCreateDto->activeFrom && $bonusCreateDto->activeTil) {
            $startAt = $bonusCreateDto->activeFrom;
            $expiredAt = $bonusCreateDto->activeTil;
        } else {
            $triggerSessions = $bonus->triggerSessions;
            $startAt = $triggerSessions->first()->start_at;
            $expiredAt = $triggerSessions->last()->end_at;
        }

        /** @var FreeSpinCrudDto */
        return FreeSpinCrudDto::fromArray(array_merge($bonusCreateDto->toArray(false), [
            'id' => $id,
            'start_at' => Carbon::parse($startAt)->format('Y-m-d H:i:s'),
            'expired_at' => Carbon::parse($expiredAt)->format('Y-m-d H:i:s'),
            'slots_id' => $bonusCreateDto->freeSpinSlot,
            'provider_id' => $bonusCreateDto->freeSpinSlotProvider,
            'supplier' => SupplierEnum::SLOTEGRATOR->value,
            'title' => 'Free Spin Bonus',
        ]));
    }

    /**
     * @param null|int[] $slotProviders
     * @param null|int[] $slots
     */
    private function syncBonusRelatedModels(Bonus $bonus, ?array $slotProviders, ?array $slots): void
    {
        if ($slotProviders === null) {
            $slots = null;
        }

        if ($bonus->casino === false) {
            $slotProviders = null;
            $slots = null;
        }

        $this->bonusRepository->syncSlots($bonus, $slots ?? []);
        $this->bonusRepository->syncSlotProviders($bonus, $slotProviders ?? []);
    }

    /**
     * @param null|array<array<string, string>> $triggerSessions
     */
    private function syncTriggerSessions(Bonus $bonus, ?array $triggerSessions): void
    {
        if (!$triggerSessions) {
            return;
        }

        $existsSessions = $this->bonusTriggerSessionService->sessionsByBonusId($bonus->id, 'start_at');

        $sessionsToCreate = collect();
        $sessionsWithoutChanges = collect();

        foreach ($triggerSessions as $triggerSession) {
            $sessionFromDb = $this->bonusTriggerSessionService
                ->getTriggerSessionsByDates($existsSessions, $triggerSession['start_at'], $triggerSession['end_at']);

            if ($sessionFromDb) {
                $sessionsWithoutChanges->push($sessionFromDb);
            } else {
                $session = $this->bonusTriggerSessionService->createByBonusIdAndDates(
                    $bonus->id,
                    $triggerSession['start_at'],
                    $triggerSession['end_at']
                );
                $sessionsToCreate->push($session);
            }
        }

        $sessionsToDelete = $existsSessions->diff($sessionsWithoutChanges);

        if ($sessionsToDelete->isNotEmpty()) {
            /** @var BonusTriggerSession $bonusTriggerSession */
            $bonusTriggerSession = $existsSessions->first();
            if ($bonusTriggerSession->start_at <= Carbon::now()) {
                throw new InternalLogicException(InternalErrorCode::BONUS_TRIGGER_SESSION_DELETE_ERROR);
            }
            $this->bonusTriggerSessionService->remove($sessionsToDelete->pluck('id')->toArray());
        }
    }

    public function getBonusById(int $bonusId): ?Bonus
    {
        return $this->bonusRepository->getBonusById($bonusId);
    }

    public function getBonusForSmarticoById(int $bonusId): ?Bonus
    {
        return $this->bonusRepository->getBonusForSmarticoById($bonusId);
    }

    public function getActualBonusById(int $bonusId): ?Bonus
    {
        return $this->bonusRepository->getActualBonusById($bonusId);
    }

    public function getActiveBonusTriggerSessionByBonusId(int $bonusId): ?BonusTriggerSession
    {
        return $this->bonusRepository->getActiveBonusTriggerSessionAtTimeByBonusId($bonusId, Carbon::now()->format('Y-m-d H:i:s'));
    }

    public function getSmarticoBonusInfoByBonusId(int $bonusId): ?BonusInfo
    {
        return $this->bonusRepository->getSmarticoBonusInfoByBonusId($bonusId);
    }

    /**
     * @param int[] $ids
     */
    public function removeByIds(array $ids): mixed
    {
        return $this->bonusRepository->removeBonuses($ids);
    }

    public function delete(Bonus $bonus): void
    {
        DB::transaction(static function () use ($bonus): void {
            $bonus->balances()->delete();
            $bonus->delete();
        });
    }

    /**
     * @throws Exception|\Exception
     */
    public function createBetbyBonus(BetbyBonusDTO $dto): Bonus
    {
        /** @var BetbySearchDTO $betbyBonusSearchDto */
        $betbyBonusSearchDto = BetbySearchDTO::fromArray(['id' => $dto->templateId]);

        $template = $this->betbyService->getTemplates($betbyBonusSearchDto);
        $template = reset($template['data']);

        $template = $template[0] ?? false;

        $this->checkTemplateForException($template);
        /** @phpstan-ignore-next-line */
        $bonusCrudDto = $this->preparedDefaultDataToBonusCrudDto($template, $dto);

        $bonus = $this->saveBonus($bonusCrudDto);

        event(
            new BonusCreateOrUpdateEvent(
                $bonus->load(['bonusSlotProviders', 'bonusSlots', 'triggerSessions', 'freespin'])
            )
        );

        return $this->hydrateSingleBonusWithAdditionalData($bonus);
    }

    /**
     * @return null|array<string, array<int, array<string, null|array<string, null|bool|int|string>|bool|int|string>>>
     *
     * @throws \Exception
     */
    public function getBetbyTemplates(BetbySearchDTO $dto): ?array
    {
        return $this->betbyService->getTemplates($dto);
    }

    /**
     * @param array<string, null|int|string|bool|array<string, null|int|string|bool>>|false $template
     * @throws Exception
     */
    private function checkTemplateForException(array|false $template): void
    {
        if (!$template) {
            throw new Exception('Betby template not found', 404);
        }

        if (!$template['is_active']) {
            throw new Exception('Betby template is not active', 400);
        }

        if ($template['type'] !== BonusTypeEnum::FREEBET->value) {
            throw new Exception('Only freebet can be saved', 400);
        }

        if (!$template['freebet_data']['is_api_amount']) {
            throw new Exception('Freebets with fixed amount is not allowed', 400);
        }

        if (
            !in_array($template['freebet_data']['type'], [
                BonusTypeEnum::FREE_MONEY->value,
                BonusTypeEnum::FREE_BET->value,
            ], true)
        ) {
            throw new Exception('This bonus type is not supported', 400);
        }
    }

    /**
     * @param array<string, null|int|string|bool|array<string, null|int|string|bool>> $template
     */
    private function preparedDefaultDataToBonusCrudDto(array $template, BetbyBonusDTO $dto): BonusCrudDto
    {
        return new BonusCrudDto([
            'client_id' => config('app.special_client_id'),
            'author_email' => $dto->authorEmail,
            'description' => $template['descriptions'],
            'name' => 'Betby bonus: ' . $template['name'],
            'max_bonuses' => [$dto->amount],
            'active' => true,
            'is_external' => true,
            'is_promo' => false,
            'casino' => false,
            'bets' => true,
            'max_transfers' => [9999999999],
            'type' => $template['freebet_data']['type'],
            'min_factor' => $template['freebet_data']['min_odd'] ?? null,
            'wager' => 1,
            'active_from' => $template['from_time'] ? Carbon::parse($template['from_time']) : null,
            'active_til' => $template['to_time'] ? Carbon::parse($template['to_time']) : null,
            'duration' => $template['days_to_use'] ? $template['days_to_use'] * 24 * 3600 : null,
            'min_bet' => $dto->amount,
            'currency' => $dto->currency,
            'data' => (new BonusDataDTO([
                'external_id' => $template['id'],
                'win_type' => $dto->winType,
                'freebet_data' => [],
            ]))->toArray(false),
            'deposit_factors' => [],
            'total_transferred' => 0,
            'total_uses' => 0,
        ]);
    }

    public function detach(int $playerId): void
    {
        /** @var BonusPlayerService $bonusPlayerService */
        $bonusPlayerService = app(BonusPlayerService::class);
        $bonusPlayerService->detachActiveBonus($playerId);
    }

    /**
     * @throws Exception
     */
    public function registrationPlayerBonus(RegistrationPlayerBonusDTO $dto): PlayerFirstBonus
    {
        $bonus = $this->getBonusById($dto->bonusId);
        if ($bonus && $bonus->is_organic) {
            throw new Exception('Bonus is organic', 400);
        }

        /** @var PlayerFirstBonus */
        return PlayerFirstBonus::query()->create([
            'player_uuid' => $dto->playerUuid,
            'bonus_id' => $dto->bonusId,
            'is_from_link' => $dto->isFromLink,
        ]);
    }

    /**
     * @param FreeSpinCrudDto $dto
     * @return array<string, mixed>
     */
    public function preparedDataForDefaultFreespinBonus(FreeSpinCrudDto $dto): array
    {
        $data = [
            'client_id' => config('app.special_client_id'),
            'author_email' => $dto->authorEmail,
            'name' => 'Free Spin Bonus',
            'type' => BonusTypeEnum::FREESPIN_BONUS->value,
            'bonuses' => [100 * ($dto->maxTransfer ?? 0)],
            'max_transfers' => [100 * ($dto->maxTransfer ?? 0)],
            'deposit_factors' => [0],
            'currency' => $dto->currency,
            'duration' => $dto->duration,
            'min_bet' => 100 * ($dto->minBet ?? 0),
            'active' => 1,
            'casino' => 1,
            'bets' => 0,
            'data' => [],
            'is_organic' => true,
            'genre_id' => $dto->bonusGenreId ?? null,
        ];

        if ($dto->wager !== null) {
            $data['wager'] = $dto->wager;
        }

        return $data;
    }

    public function isBonusRelatedToSlot(int $bonusId, int $slotId): bool
    {
        return $this->bonusRepository->isBonusRelatedToSlot($bonusId, $slotId);
    }

    /**
     * @return int[]
     */
    public function getBonusesIdsBySlotId(int $slotId): array
    {
        $bonusSlots = $this->bonusRepository->getBonusSlotsBySlotId($slotId);

        return $bonusSlots->pluck('bonus_id')->unique()->flatten()->filter()->toArray();
    }

    public function isWagerPayable(IsWagerPayableDTO $dto): bool
    {
        return Cache::tags(self::IS_WAGER_PAYABLE)->remember(
            'bonus_' . $dto->bonusId . '_slot_' . $dto->slotId,
            Carbon::now()->addMinutes(5),
            function () use ($dto) {
                $bonus = $this->bonusRepository->getActualBonusById($dto->bonusId);
                if (!$bonus) {
                    return true;
                }
                $bonus->load(['bonusSlotProviders', 'bonusSlots']);

                if ($this->isGeneral($dto->bonusId)) {
                    return $dto->isBonusReady;
                }

                return $bonus->bonusSlots->isNotEmpty()
                    ? $bonus->bonusSlots->pluck('slot_id')->contains($dto->slotId)
                    : $bonus->bonusSlotProviders->pluck('slot_provider_id')->contains($dto->externalProviderId);
            }
        );
    }

    public function isGeneral(int $bonusId): bool
    {
        return Cache::tags(self::IS_GENERAL_BONUS)->remember(
            "bonus_$bonusId",
            Carbon::now()->addMinutes(5),
            function () use ($bonusId) {
                $bonus = $this->bonusRepository->getActualBonusById($bonusId);
                if (!$bonus) {
                    return true;
                }
                $bonus->load(['bonusSlotProviders', 'bonusSlots']);

                return $bonus->bonusSlots->isEmpty() && $bonus->bonusSlotProviders->isEmpty();
            }
        );
    }
}
