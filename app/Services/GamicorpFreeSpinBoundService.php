<?php

declare(strict_types=1);

namespace App\Services;

use App\Clients\GamicorpClient;
use App\Enums\FreeSpinBoundStatusEnum;
use App\Models\FreeSpinBound;
use App\Models\FreeSpin;
use App\Services\Dto\GamicorpFreeSpinBoundCreateDto;
use App\Services\Dto\GamicorpFreeSpinCreateDto;
use App\Services\Dto\GamicorpResponseDTO;
use App\Services\Dto\PlayerDto;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

class GamicorpFreeSpinBoundService
{
    public function __construct(
        private readonly PlayerService $playerService,
        private readonly SlotService $slotService,
        private readonly GamicorpClient $gamicorpClient,
    ) {
    }

    public function createFreeSpinBound(FreeSpin $freespin, PlayerDto $player, int $freeSpinCount): GamicorpResponseDTO
    {
        $dto = $this->getGamicorpFreeSpinBoundCreateDto($freespin, $player->id, $freeSpinCount);

        /** @var FreeSpinBoundService $freeSpinBoundService */
        $freeSpinBoundService = app(FreeSpinBoundService::class);
        $freeSpinBound = $freeSpinBoundService->createFromFreeSpinBound($dto, $player->id);

        if (Carbon::parse($freespin->expired_at) < now()) {
            $freeSpinBound->message = 'Freespin is expired';
            $freeSpinBound->save();
        }

        if ($player->blocked) {
            $freeSpinBound->message = 'Player is blocked';
            $freeSpinBound->save();
        }

        if ($player->currency !== $freespin->currency) {
            $freeSpinBound->message = 'Different currency is used';
            $freeSpinBound->save();
        }

        $gamicorpFreeSpinData = $this->getGamicorpFreeSpinCreateDto($dto, $player, $freeSpinBound->id);

        return $this->gamicorpClient->createFreeSpin($gamicorpFreeSpinData);
    }

    public function attach(FreeSpin $freeSpin, int $playerId): bool
    {
        DB::beginTransaction();

        try {
            $player = $this->playerService->getPlayerById($playerId);
            $gamicorpResponseDTO = $this->createFreeSpinBound($freeSpin, $player, $freeSpin->count);

            /** @var FreeSpinBoundService $freeSpinBoundService */
            $freeSpinBoundService = app(FreeSpinBoundService::class);
            if ($gamicorpResponseDTO->statusCode !== 200) {
                $freeSpinBoundService->update([
                    'message' => $gamicorpResponseDTO->errorMessage,
                    'status' => FreeSpinBoundStatusEnum::INACTIVE->value,
                ], [$gamicorpResponseDTO->freespinBoundId]);
                DB::commit();

                return false;
            }

            $freeSpinBoundService->update([
                'is_active' => true,
                'message' => $gamicorpResponseDTO->status,
                'status' => FreeSpinBoundStatusEnum::ACTIVE->value,
            ], [$gamicorpResponseDTO->freespinBoundId]);
            DB::commit();

            return true;
        } catch (RuntimeException) {
            DB::rollBack();

            return false;
        }
    }

    public function cancelFreespinBound(FreeSpinBound $freeSpinBound): bool
    {
        DB::beginTransaction();

        try {
            $gamicorpResponseDTO = $this->gamicorpClient->cancelFreeSpinBound($freeSpinBound->id);

            /** @var FreeSpinBoundService $freeSpinBoundService */
            $freeSpinBoundService = app(FreeSpinBoundService::class);
            if ($gamicorpResponseDTO->statusCode !== 200) {
                $freeSpinBoundService->update(['message' => $gamicorpResponseDTO->errorMessage], [$freeSpinBound->id]);
                DB::commit();

                return false;
            }

            $freeSpinBoundService->update([
                'is_active' => false,
                'canceled_at' => Carbon::now(),
                'is_archived' => true,
                'message' => 'Canceled By user',
                'status' => FreeSpinBoundStatusEnum::DEACTIVATED->value,
            ], [$freeSpinBound->id]);
            DB::commit();

            return true;
        } catch (Throwable) {
            DB::rollBack();

            return false;
        }
    }

    public function getGamicorpFreeSpinBoundCreateDto(
        FreeSpin $freespin,
        int $playerId,
        int $freeSpinCount
    ): GamicorpFreeSpinBoundCreateDto {
        if ($freespin->slot_external_id) {
            $externalId = $freespin->slot_external_id;
        } else {
            $slot = $this->slotService->getSlotById($freespin->slot_id);
            $externalId = $slot->externalId;
        }

        return GamicorpFreeSpinBoundCreateDto::fromArray([
            'id' => $freespin->id,
            'provider_id' => $freespin->provider_id,
            'bet' => $freespin->bet,
            'bet_id' => $freespin->bet_id,
            'count' => $freeSpinCount,
            'denomination' => $freespin->denomination,
            'currency' => $freespin->currency,
            'start_at' => (string)Carbon::now()->timestamp,
            'expired_at' => (string)Carbon::parse($freespin->expired_at)->timestamp,
            'duration' => $freespin->duration,
            'external_id' => $externalId,
            'bonus_id' => $freespin->bonus_id,
            'player_id' => $playerId,
            'options' => $freespin->options,
        ]);
    }

    public function getGamicorpFreeSpinCreateDto(
        GamicorpFreeSpinBoundCreateDto $dto,
        PlayerDto $player,
        int $freeSpinBoundId
    ): GamicorpFreeSpinCreateDto {
        $playerBalanceId = $this->playerService->getBalanceIdByPlayerUuid($player->uuid);

        return GamicorpFreeSpinCreateDto::fromArray([
            'token' => config('auth.gamicorp_merchant_token'),
            'provider_id' => $dto->providerId,
            'freebet_id' => $freeSpinBoundId,
            'user_label' => $playerBalanceId . '--' . $player->uuid,
            'game_uuids' => [$dto->externalId],
            'free_rounds_count' => $dto->count,
            'bet_amount' => $dto->bet ? $dto->bet / 100 : null,
            'bet_level' => $dto->betId,
            'currency' => $dto->currency,
            'expire_date' => Carbon::createFromTimestamp($dto->expiredAt)->format('Y-m-d H:i:s'),
            'expiration_duration' => $dto->duration,
            'user' => [
                'firstname' => $player->firstName,
                'lastname' => $player->lastName,
                'nickname' => $player->username,
                'date_of_birth' => $player->birth,
                'gender' => $player->gender,
                'country' => $player->country,
                'city' => $player->city,
                'registered_at' => $player->createdAt ? Carbon::make($player->createdAt)->format('Y-m-d') : null,
            ],
            'options' => $dto->options,
        ]);
    }
}
