<?php

namespace App\Services;

use App\Enums\SupplierEnum;
use App\Events\BonusCreateOrUpdateEvent;
use App\Events\FreeSpinCudEvent;
use App\Events\FreeSpinResendEvent;
use App\Exceptions\Exception;
use App\Jobs\FreeSpins\FreeSpinsCancelJob;
use App\Jobs\FreeSpins\FreeSpinsUpdateJob;
use App\Models\FreeSpin;
use App\Repositories\FreeSpinBoundRepository;
use App\Repositories\FreeSpinRepository;
use App\Services\Dto\FreeSpinCrudDto;
use App\Services\Dto\FreeSpinSearchDto;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection as BaseCollection;
use Exception as BaseException;
use Throwable;

/**
 * Class FreeSpinService
 */
class FreeSpinService
{
    public const FINISH_PROCESS_FREE_SPIN_STATUS = 0;
    public const IN_PROCESS_FREE_SPIN_STATUS = 1;

    public const CANCEL_FREE_SPIN_STATUS = 2;

    public function __construct(
        private readonly FreeSpinRepository $freeSpinRepository,
        private readonly FreeSpinBoundRepository $freeSpinBoundRepository,
        private readonly BonusService $bonusService,
        private readonly FreeSpinBoundService $freeSpinBoundService,
        private readonly PlayerService $playerService,
        private readonly SlotService $slotService,
        private readonly SlotProviderService $slotProviderService,
    ) {
    }

    public function list(FreeSpinSearchDto $freeSpinSearchDto): LengthAwarePaginator
    {
        $freeSpins = $this->freeSpinRepository->getWithPagination($freeSpinSearchDto);

        $this->hydrateFreeSpinsWithSlot($freeSpins);

        return $freeSpins;
    }

    /**
     * @param BaseCollection<int, FreeSpin>|LengthAwarePaginator $freeSpins
     *
     * Hydrates the FreeSpins with corresponding Slot based on the FreeSpin's slot_id
     *
     * @return BaseCollection<int, FreeSpin>|LengthAwarePaginator
     */
    public function hydrateFreeSpinsWithSlot(
        BaseCollection|LengthAwarePaginator $freeSpins
    ): BaseCollection|LengthAwarePaginator {
        $uniqueSlotsIds = $freeSpins->unique('slot_id')->pluck('slot_id')->filter()->toArray();
        $slots = $this->slotService->getSlotListByIds($uniqueSlotsIds);

        $freeSpins->map(static function (FreeSpin $freeSpin) use ($slots): void {
            $slot = $slots[$freeSpin->slot_id] ?? null;
            $freeSpin->setAttribute('slot', $slot);
        });

        return $freeSpins;
    }

    /**
     * Hydrates the FreeSpins with corresponding Provider based on the FreeSpin's provider_id.
     */
    public function hydrateFreeSpinsWithProvider(
        BaseCollection|LengthAwarePaginator $freeSpins
    ): void {
        $providersIds = $freeSpins->unique('provider_id')->pluck('provider_id')->filter()->toArray();
        $providers = $this->slotProviderService->getSlotProvidersByIds($providersIds);

        $freeSpins->map(static function (FreeSpin $freeSpin) use ($providers): void {
            $slotProvider = $providers[$freeSpin->provider_id] ?? null;
            $freeSpin->setAttribute('provider', $slotProvider);
        });
    }

    private function hydrateSingleFreeSpinWithSlot(FreeSpin $freespin): FreeSpin
    {
        return $this->hydrateFreeSpinsWithSlot(collect([$freespin]))->first();
    }

    public function getFreeSpinDetailById(int $id): FreeSpin
    {
        return $this->freeSpinRepository->getFreeSpinDetailById($id);
    }

    public function getFreeSpinById(int $id): FreeSpin
    {
        $freeSpin = $this->freeSpinRepository->getFreeSpinDetailById($id);

        if ($freeSpin->bonus_id && !$freeSpin->bonus->isUsedGenre()) {
            $this->bonusService->hydrateSingleBonusWithAdditionalData($freeSpin->bonus);
        }

        $collection = new BaseCollection([$freeSpin]);
        $this->hydrateFreeSpinsWithSlot($collection);
        $this->hydrateFreeSpinsWithPlayers($id, $collection);

        return $collection->first();
    }

    /**
     * @uses getFistFreeSpinById
     */
    public function getFistFreeSpinById(int $id): ?FreeSpin
    {
        return $this->freeSpinRepository->getFirstFreeSpinById($id);
    }

    public function getFistFreeSpinFromCacheById(int $id): ?FreeSpin
    {
        return Cache::remember("freespin_data_for_id_$id", 600, fn() => $this->freeSpinRepository->getFirstFreeSpinById($id));

    }

    /**
     * @param BaseCollection<int, FreeSpin>|LengthAwarePaginator $freeSpins
     */
    private function hydrateFreeSpinsWithPlayers(int $id, BaseCollection|LengthAwarePaginator $freeSpins): void
    {
        $uniquePlayersIds = $this->freeSpinBoundRepository->getLimitedPlayersByFreeSpinId($id)->pluck('player_id')->toArray();

        $players = $this->playerService->getPlayersByIds($uniquePlayersIds);

        $freeSpins->map(static function (FreeSpin $freeSpin) use ($players, $uniquePlayersIds): void {
            $freeSpinPlayers = collect($uniquePlayersIds)
                ->map(static fn($playerId) => $players->get($playerId))
                ->filter()
                ->values();
            $freeSpin->setAttribute('players', $freeSpinPlayers);
        });
    }

    /**
     * @throws Exception
     */
    public function resendFreeSpin(int $freespinId, ?int $freespinBoundId): void
    {
        $freespin = $this->freeSpinRepository->getFreeSpinDetailById($freespinId);

        if ($freespinBoundId) {
            $freespinBound = $this->freeSpinBoundRepository->getFreeSpinBoundDetailById($freespinBoundId);

            if ($freespinBound->is_active) {
                throw new Exception('Freespin is already given to the user');
            }

            $freespinBoundIdsArray[] = $freespinBoundId;
        } else {
            $freespinBoundIdsArray = $this->freeSpinBoundRepository->getAllNotSentBoundsByFreespinId($freespinId);
        }

        $this->freeSpinBoundService->update(['start_at' => Carbon::now()->addMinute()], $freespinBoundIdsArray);

        event(new FreeSpinResendEvent($freespin, $freespinBoundIdsArray));
    }

    /**
     * @throws Throwable
     */
    public function store(FreeSpinCrudDto $dto): FreeSpin
    {
        // unit test will work using construction DB::transaction(fn() => {...})
        /** @var FreeSpin $freeSpin */
        $freeSpin = FreeSpin::query()->getConnection()->transaction(function () use ($dto) {
            $bonus = $dto->bonusBalance ? $this->bonusService->createOrUpdateDefaultFreespinBonus($dto) : null;
            $freeSpin = $this->saveFreeSpin($dto, FreeSpin::MANUAL_TYPE, $bonus?->id);

            if ($dto->players) {
                $playersIdsArray = $this->getPlayersArray($dto->players);

                $this->freeSpinBoundService->attachFreeSpinToPlayers($freeSpin, $playersIdsArray);
            }

            if ($dto->bonusBalance) {
                $freeSpin->setRelation('bonus', $bonus);
            }

            return $freeSpin;
        });

        $this->hydrateSingleFreeSpinWithSlot($freeSpin);

        event(new FreeSpinCudEvent($freeSpin->id, $freeSpin, null, $freeSpin->author_email));

        if ($dto->bonusBalance) {
            event(new BonusCreateOrUpdateEvent(
                $freeSpin->bonus->setRelation('freespin', $freeSpin->replicate()->setRelation('bonus', null))
            ));
        }

        return $freeSpin;
    }

    /**
     * @param array<array<string, string>> $players
     *
     * @return int[]
     */
    public function getPlayersArray(array $players): array
    {
        $playersCollection = collect($players)->unique('uuid');
        $playerUUIDs = $playersCollection->pluck('uuid')->toArray();
        $playersFromDB = $this->playerService->getPlayersByUUIDs($playerUUIDs);

        $missingPlayers = array_diff($playerUUIDs, $playersFromDB->pluck('uuid')->toArray());

        if (count($missingPlayers) > 0) {
            Log::info('Этих игроков нет в базе: ' . implode(', ', $missingPlayers));
        }

        return $playersFromDB->pluck('id')->toArray();
    }

    public function saveFreeSpin(FreeSpinCrudDto $dto, string $type, ?int $bonusId = null): FreeSpin
    {
        $data = [
            'type' => $type,
            'start_at' => $dto->startAt ?? Carbon::now()->format('Y-m-d H:i:s'),
            'in_process' => $dto->supplier === SupplierEnum::GAMICORP_INTEGRATION->value ? 0 : 1,
        ];

        if ($bonusId) {
            $data['bonus_id'] = $bonusId;
        }

        $freespin = $this->fillFreeSpinData(array_merge($dto->getFreeSpinData(), $data), $dto->id);
        $this->freeSpinRepository->save($freespin);

        return $freespin;
    }

    /**
     * @param array<string, int|string|bool> $data
     */
    public function fillFreeSpinData(array $data, ?int $id): FreeSpin
    {
        $freespin = $id ? $this->freeSpinRepository->getFreeSpinDetailById($id) : new FreeSpin();
        $freespin->fill($data);

        return $freespin;
    }

    public function changeFreeSpinProcessStatus(int $id, int $status, bool $withActivity = false): void
    {
        if ($withActivity) {
            $this->freeSpinRepository->changeFreeSpinProcessStatusAndActivity($id, $status);
        } else {
            $this->freeSpinRepository->changeFreeSpinProcessStatus($id, $status);
        }
    }

    /**
     * @throws Exception
     * @throws BaseException
     */
    public function update(FreeSpinCrudDto $dto): FreeSpin
    {
        DB::beginTransaction();

        try {
            $prevBonus = null;
            $prevFreespin = null;
            if ($dto->id) {
                $prevFreespin = $this->freeSpinRepository->getFirstFreeSpinById($dto->id);
                $prevBonus = $prevFreespin?->bonus;

                $this->hydrateSingleFreeSpinWithSlot($prevFreespin);

                if ($prevBonus) {
                    $prevBonus->load(['bonusSlotProviders', 'bonusSlots', 'triggerSessions', 'freespin']);
                    $this->bonusService->hydrateSingleBonusWithAdditionalData($prevBonus);
                }
            }

            $freespin = $this->saveFreeSpin($dto, FreeSpin::MANUAL_TYPE);
            $bonus = $this->bonusService->createOrUpdateDefaultFreespinBonus($dto, $freespin->bonus_id);
            $freespin->setRelation('bonus', $bonus);

            $this->hydrateSingleFreeSpinWithSlot($freespin);

            event(new BonusCreateOrUpdateEvent($bonus, $prevBonus));

            if ($dto->players) {
                $playersIdsArray = $this->getPlayersArray($dto->players);
                $this->freeSpinBoundService->attachFreeSpinToPlayers($freespin, $playersIdsArray);
            }

            event(new FreeSpinCudEvent($freespin->id, $freespin, $prevFreespin, $freespin->author_email));

            DB::commit();

            return $freespin;
        } catch (Throwable) {
            DB::rollBack();

            throw new Exception('Error updating free spin', 400);
        }
    }

    /**
     * @return array<string, bool>
     */
    public function checkFreeSpinName(string $name, ?int $id = null): array
    {
        return ['exists' => $this->freeSpinRepository->checkFreeSpinName($name, $id)];
    }

    /**
     * @throws Exception
     * @throws BaseException
     */
    public function updateFreeSpin(FreeSpinCrudDto $dto): FreeSpin
    {
        $exists = $this->checkFreeSpinName($dto->name, $dto->id);
        if ($exists['exists']) {
            throw new Exception('There is already a free spin with the same name', 400);
        }
        $freespin = $this->getFreeSpinDetailById($dto->id);
        if ($freespin->in_process) {
            throw new Exception('Previous request the creation of free spins for players is still in progress, please try later!', 400);
        }

        if ($freespin->supplier === SupplierEnum::GAMICORP_INTEGRATION->value) {
            if ($this->freeSpinBoundService->hasFreeSpinActiveBound($dto->id)) {
                throw new Exception('Freespin has active bounds', 400);
            }

            return $this->update($dto);
        }

        if (Carbon::parse($freespin->start_at) <= Carbon::now()) {
            throw new Exception('Edit is not possible', 400);
        }

        $this->changeFreeSpinProcessStatus($freespin->id, self::IN_PROCESS_FREE_SPIN_STATUS);

        dispatch(new FreeSpinsUpdateJob($dto->id, $dto->slotsId, $dto));

        return $this->hydrateSingleFreeSpinWithSlot($freespin);
    }

    public function removeFreeSpin(int $id): void
    {
        $freespin = $this->freeSpinRepository->getFreeSpinDetailById($id);
        event(new FreeSpinCudEvent($freespin->id, null, null, $freespin->author_email));
        $this->freeSpinRepository->remove($freespin);
    }

    public function getFreeSpinByBonusId(int $bonusId): FreeSpin
    {
        return $this->freeSpinRepository->getFreeSpinDetailByBonusId($bonusId);
    }

    public function delete(int $id, ?string $authorEmail = null): bool
    {
        dispatch(new FreeSpinsCancelJob($id, $authorEmail));

        return true;
    }
}
