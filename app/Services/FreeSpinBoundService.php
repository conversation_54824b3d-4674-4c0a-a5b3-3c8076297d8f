<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\FreeSpinBoundStatusEnum;
use App\Enums\SupplierEnum;
use App\Events\FreeSpinCreateEvent;
use App\Exceptions\GamicorpException;
use App\Jobs\FreeSpins\FreeSpinBoundCancelJob;
use App\Models\DataSubscriptions;
use App\Models\FreeSpin;
use App\Models\FreeSpinBound;
use App\Repositories\FreeSpinBoundRepository;
use App\Services\Dto\FreeSpinBoundSearchDTO;
use App\Services\Dto\FreeSpinIntegrationSlotEgratorDto;
use App\Services\Dto\GamicorpFreeSpinBoundCreateDto;
use App\Services\Dto\GetFreeSpinBoundsDTO;
use App\Services\Dto\UpdateBoundDTO;
use App\ValueObjects\BonusBalanceStatus;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\QueryException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection as BaseCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use PDOException;
use RuntimeException;
use Throwable;

class FreeSpinBoundService
{
    private const BETS_URI = '/api/v1/freespins/set';
    private const CANCEL_URI = '/api/v1/freespins/cancel';

    public function __construct(
        public readonly FreeSpinBoundRepository $freeSpinBoundRepository,
        public readonly BonusBalanceService $bonusBalanceService,
        public readonly SendToSlotegratorHttpService $sendToSlotegratorHttpService,
        public readonly SlotService $slotService,
        public readonly GamicorpFreeSpinBoundService $gamicorpFreeSpinBoundService,
        public readonly PlayerService $playerService,
        public readonly SlotegratorFreeSpinBoundService $slotegratorFreeSpinBoundService,
    ) {
    }

    public function findById(int $id): ?FreeSpinBound
    {
        return $this->freeSpinBoundRepository->getFreeSpinBoundDetailById($id);
    }

    public function createFromFreeSpinBound(
        FreeSpinIntegrationSlotEgratorDto|GamicorpFreeSpinBoundCreateDto $dto,
        int $playerId
    ): FreeSpinBound {

        $freeSpinBound = new FreeSpinBound();
        $freeSpinBound->uuid = Str::random(32);
        $freeSpinBound->free_spin_id = $dto->id;
        $freeSpinBound->player_id = $playerId;
        $freeSpinBound->bet = $dto->bet;
        $freeSpinBound->count_left = $dto->count;
        $freeSpinBound->is_active = false;
        $freeSpinBound->bonus_id = $dto->bonusId;
        $freeSpinBound->bet_id = $dto->betId;
        $freeSpinBound->start_at = Carbon::createFromTimestamp($dto->startAt)->addMinute();
        $freeSpinBound->expire_at = !empty($dto->duration) ? $freeSpinBound->start_at->addSeconds(
            $dto->duration
        ) : Carbon::createFromTimestamp($dto->expiredAt);
        $this->freeSpinBoundRepository->save($freeSpinBound);

        return $freeSpinBound;
    }

    /**
     * @param array<string, int|string|bool|Carbon> $data
     * @param int[] $ids
     */
    public function update(array $data, array $ids): int
    {
        return $this->freeSpinBoundRepository->updateFreeSpinBounds($data, $ids);
    }

    /**
     * @return Collection<int, FreeSpinBound>
     */
    public function getPlayersByFreeSpinId(int $freeSpinId): Collection
    {
        return $this->freeSpinBoundRepository->getPlayersByFreeSpinId($freeSpinId);
    }

    public function search(FreeSpinBoundSearchDTO $dto): LengthAwarePaginator
    {
        return $this->freeSpinBoundRepository->search($dto);
    }

    public function removeCanceledFreeSpinFromDB(int $freeSpinId): void
    {
        /** @var null|FreeSpinBound $freeSpinBound */
        $freeSpinBound = $this->freeSpinBoundRepository->getFreeSpinBoundsByFreespinId($freeSpinId)->first();
        $this->freeSpinBoundRepository->removeFreeSpinBoundsByFreeSpinIds([$freeSpinId]);
        $bonusId = $freeSpinBound?->bonus_id;

        if (null !== $bonusId) {
            /** @var BonusService $bonusService */
            $bonusService = app(BonusService::class);
            $bonusService->removeByIds([$bonusId]);
        }
    }

    /**
     * @param Collection<int, FreeSpinBound> $freeSpinBounds
     *
     * @throws Exception
     */
    public function cancelFreeSpins(Collection $freeSpinBounds, DataSubscriptions $subscription, bool $isCancelBound = false): bool
    {
        $removeData['freeSpins'] = [];
        $removeData['subscriberId'] = (int)$subscription->sub_id;

        /** @var FreeSpinBound $item */
        foreach ($freeSpinBounds as $item) {
            $removeData['freeSpins'][] = ['freespin_id' => (string)$item->id];
        }

        if ($removeData['freeSpins']) {
            $response = $this->sendToSlotegratorHttpService->makePostRequestToSlotEgrator(self::CANCEL_URI, $subscription->game_token, $removeData);
            if (!$response->successful()) {
                throw new Exception('Server error, please try later !', 400);
            }

            $items = json_decode(json_encode($response->object()), true);
            foreach ($items as $item) {
                if ($item['status'] === false) {
                    Log::debug('Can not remove free spin for this user', [
                        'freespinId' => $item['freespinId'],
                        'message' => $item['message'],
                    ]);
                } elseif ($isCancelBound) {
                    $freeSpinBound = $this->freeSpinBoundRepository->getFreeSpinBoundDetailById((int)$item['freespinId']);
                    $this->update([
                        'is_active' => false,
                        'canceled_at' => Carbon::now(),
                        'is_archived' => true,
                        'message' => 'Canceled By user',
                    ], [$freeSpinBound->id]);
                    $this->bonusBalanceService->updateBonusBalance([
                        'player_id' => $freeSpinBound->player_id,
                        'bonus_id' => $freeSpinBound->bonus_id,
                        'active' => false,
                        'status' => BonusBalanceStatus::WAITING,
                    ], ['status' => BonusBalanceStatus::CANCELED]);
                }
            }
        }

        return true;
    }

    /**
     * @param array<string, int|list<array<string, string>>> $data
     * @throws Exception
     */
    public function sendFreeSpinsData(string $accessToken, int $freeSpinBoundId, array $data): void
    {
        Log::info('Start sendFreeSpinsData', ['data' => $data,
            'freeSpinBoundId' => $freeSpinBoundId]);
        $response = $this->sendToSlotegratorHttpService->makePostRequestToSlotEgrator(self::BETS_URI, $accessToken, $data);
        Log::info('End sendFreeSpinsData', ['freeSpinBoundId' => $freeSpinBoundId,
            'isSuccessful' => $response->successful()]);
        $isSuccessful = $response->successful();
        $items = $isSuccessful ? json_decode(json_encode($response->object()), true) : $data['freeSpins'];
        Log::info('items sendFreeSpinsData', ['freeSpinBoundId' => $freeSpinBoundId,
            'items' => $items]);
        foreach ($items as $item) {
            $this->update([
                'is_active' => $isSuccessful ? $item['status'] : false,
                'message' => $isSuccessful ? $item['message'] : 'Server error, please try later !',
            ], [$freeSpinBoundId]);

            if (!$isSuccessful || ($item['status'] ?? null) === false) {
                Log::debug('Can not set free spin for this user', [
                    'freespinId' => $freeSpinBoundId,
                    'message' => $isSuccessful ? $item['message'] : 'Server error, please try later !',
                ]);
                if (!$isSuccessful) {
                    throw new Exception('Server error, please try later !', 400);
                }
            }
        }
    }

    public function existFreeSpinBoundByBonusId(int $bonusId): bool
    {
        return $this->freeSpinBoundRepository->existFreeSpinBoundByBonusId($bonusId);
    }

    /**
     * @return Collection<int, FreeSpinBound>
     */
    public function searchFreeSpinBoundsWithoutPagination(GetFreeSpinBoundsDTO $dto): Collection
    {
        return $this->freeSpinBoundRepository->searchFreeSpinBoundsWithoutPagination($dto);
    }

    public function updateBound(UpdateBoundDTO $dto): FreeSpinBound
    {
        return $this->freeSpinBoundRepository->updateBound($dto);
    }

    /**
     * @return BaseCollection<int, FreeSpinBound>|\Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getPlayerFreeSpinsByStatus(int $playerId, string $status): BaseCollection|\Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        if ($status === 'active') {
            $freeSpinBounds =  $this->freeSpinBoundRepository->getPlayerActiveFreeSpinBounds($playerId);
        } else {
            $freeSpinBounds = $this->freeSpinBoundRepository->getPlayerArchivedFreeSpinBounds($playerId);
        }

        return $this->hydrateFreeSpinBoundsWithSlot($freeSpinBounds);
    }

    /**
     * @param BaseCollection<int, FreeSpinBound>|\Illuminate\Contracts\Pagination\LengthAwarePaginator $freeSpinBounds
     * @return BaseCollection<int, FreeSpinBound>|LengthAwarePaginator
     */
    private function hydrateFreeSpinBoundsWithSlot(
        BaseCollection|\Illuminate\Contracts\Pagination\LengthAwarePaginator $freeSpinBounds
    ): BaseCollection|LengthAwarePaginator {
        $uniqueSlotsIds = $freeSpinBounds->pluck('freespinBonusSlotWithTrashed.slot_id')->unique()->filter()->toArray();
        $slots = $this->slotService->getSlotListByIds($uniqueSlotsIds);

        $freeSpinBounds->map(static function (FreeSpinBound $freeSpin) use ($slots): void {
            $slot = $slots[$freeSpin->freespinBonusSlotWithTrashed?->slot_id] ?? null;
            $freeSpin->setAttribute('slot', $slot);
        });

        return $freeSpinBounds;
    }

    public function getFreeSpinBoundList(FreeSpinBoundSearchDTO $dto): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $freeSpinBounds = $this->search($dto);
        $this->hydrateFreeSpinsBoundsWithPlayers($freeSpinBounds);

        return $freeSpinBounds;
    }

    /**
     * @param BaseCollection<int, FreeSpinBound>|\Illuminate\Contracts\Pagination\LengthAwarePaginator $freeSpinBounds
     */
    public function hydrateFreeSpinsBoundsWithPlayers(BaseCollection|\Illuminate\Contracts\Pagination\LengthAwarePaginator $freeSpinBounds): void
    {
        $uniquePlayersIds = $freeSpinBounds->pluck('player_id')->unique()->flatten()->filter()->toArray();
        $players = $this->playerService->getPlayersByIds($uniquePlayersIds);

        $freeSpinBounds->map(static function (FreeSpinBound $bound) use ($players): void {
            $bound->setAttribute('playerInfo', $players->get($bound->player_id));
        });
    }

    /**
     * @throws \App\Exceptions\Exception
     * @throws Exception
     */
    public function cancelFreeSpinBound(int $freeSpinBoundId, int $playerId): bool
    {
        /** @var FreeSpin $freespin */
        $freespin = $this->freeSpinBoundRepository->getFreeSpinDetailByBoundId($freeSpinBoundId);

        $freeSpinBound = $this->freeSpinBoundRepository->getFreeSpinBoundDetailById($freeSpinBoundId);

        if ($freeSpinBound->player_id !== $playerId) {
            throw new Exception('Unauthorized, please pass a valid core bearer token');
        }

        if ($freespin->supplier === SupplierEnum::GAMICORP_INTEGRATION->value) {
            return $this->gamicorpFreeSpinBoundService->cancelFreespinBound($freeSpinBound);
        }

        dispatch(new FreeSpinBoundCancelJob($freeSpinBound->id, $freespin->slot_id));

        return true;
    }

    /**
     * @param int[] $playersIdsArray
     * @throws Exception
     */
    public function attachFreeSpinToPlayers(FreeSpin $freespin, array $playersIdsArray): void
    {
        if ($freespin->supplier === SupplierEnum::GAMICORP_INTEGRATION->value) {
            foreach ($playersIdsArray as $playerId) {
                $this->attachFreeSpinToPlayer($freespin->bonus_id, $playerId);
            }
        } else {
            event(new FreeSpinCreateEvent(
                $freespin,
                $playersIdsArray,
                Carbon::parse($freespin->start_at),
                Carbon::parse($freespin->expired_at)
            ));
        }
    }

    /**
     * @throws Exception
     */
    public function attachFreeSpinToPlayer(int $bonusId, int $playerId): bool
    {
        /** @var FreeSpinService $freeSpinService */
        $freeSpinService = app(FreeSpinService::class);
        $freespin = $freeSpinService->getFreeSpinByBonusId($bonusId);

        if ($freespin->supplier === SupplierEnum::GAMICORP_INTEGRATION->value) {
            return $this->gamicorpFreeSpinBoundService->attach($freespin, $playerId);
        }

        if ($freespin->supplier === SupplierEnum::SLOTEGRATOR->value) {
            return $this->slotegratorFreeSpinBoundService->attach($freespin, $playerId);
        }

        return false;
    }

    /**
     * @throws Exception
     *
     * @return array<string, string|int|float>
     */
    public function freeSpinFinish(int $freeSpinBoundId, int $amount): array
    {
        try {
            $freeSpinBound = $this->freeSpinBoundRepository->getFreeSpinBoundById($freeSpinBoundId);

            if (!$freeSpinBound) {
                throw new GamicorpException("FreeBet with id - $freeSpinBoundId is not valid or doesn't exist", 400);
            }

            if (!$freeSpinBound->freespin->is_active) {
                throw new GamicorpException('FreeSpin is not active', 400);
            }

            if (!in_array(
                $freeSpinBound->status,
                [FreeSpinBoundStatusEnum::ACTIVE->value, FreeSpinBoundStatusEnum::EXPIRED->value],
                true
            )) {
                throw new GamicorpException('FreeSpin bound is not active', 400);
            }

            if (Carbon::parse($freeSpinBound->expire_at)->addMinutes(10) < Carbon::now()) {
                throw new GamicorpException('FreeSpin bound is expired', 400);
            }

            $balance = DB::transaction(function () use ($freeSpinBound, $amount) {
                $this->bonusBalanceService->createBalanceFromFreeSpin(
                    $freeSpinBound->bonus_id,
                    $freeSpinBound->player_id,
                    $amount
                );

                $player = $this->playerService->getPlayerById($freeSpinBound->player_id);

                $balance = $this->playerService->getBalanceByPlayerUuid($player->uuid);

                $this->finishFreeSpinBound($freeSpinBound->id, $amount);

                return $balance / 100;
            });
        } catch (Throwable $e) {
            if ($e instanceof QueryException) {
                throw new GamicorpException('Mysql query error', 400);
            }

            if ($e instanceof PDOException) {
                throw new GamicorpException('Database connection refused', 400);
            }

            if ($e instanceof GamicorpException) {
                throw new GamicorpException($e->getMessage(), $e->getCode());
            }

            if ($e instanceof RuntimeException) {
                throw new GamicorpException('Freespin finish error', 400);
            }
        }

        return [
            'status' => 'ok',
            'balance' => $balance ?? 0,
        ];
    }

    public function finishFreeSpinBound(int $freeSpinBoundId, int $amount): int
    {
        $status = $amount > 0 ? FreeSpinBoundStatusEnum::WIN->value : FreeSpinBoundStatusEnum::LOST->value;

        return $this->update([
            'is_active' => false,
            'is_archived' => true,
            'total_win' => $amount,
            'count_left' => 0,
            'message' => 'Finished by user',
            'status' => $status,
        ], [$freeSpinBoundId]);
    }

    public function hasFreeSpinActiveBound(int $id): bool
    {
        return $this->freeSpinBoundRepository->hasFreeSpinActiveBound($id);
    }
}
