<?php

namespace App\Services;

use App\Models\FreeSpin;
use App\Models\FreeSpinHistoryLog;
use App\Repositories\FreeSpinHistoryLogRepository;
use App\Services\Dto\FreeSpinHistoryLogDTO;
use App\Services\Dto\SlotDto;
use App\Services\Dto\SlotProviderDto;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

/**
 * Class FreeSpinHistoryLogService
 */
class FreeSpinHistoryLogService
{
    public const FIELDS = [
        'supplier', 'aggregator', 'name', 'title', 'bet',
        'count', 'denomination', 'currency', 'start_at', 'expired_at',
        'bet_id', 'slot_id', 'provider_id', 'duration'];

    public function __construct(
        private readonly FreeSpinHistoryLogRepository $freeSpinRepository,
        private readonly SlotService $slotService,
        private readonly SlotProviderService $slotProviderService,
    ) {
    }

    public function list(FreeSpinHistoryLogDTO $dto): LengthAwarePaginator
    {
        return $this->freeSpinRepository->getLogsByFreeSpinId($dto);
    }

    public function createLog(?FreeSpin $prevFreeSpin, FreeSpin $currentFreeSpin, ?string $authorEmail): void
    {
        $diff = $this->getDiff($prevFreeSpin?->toArray() ?? [], $currentFreeSpin->toArray());
        if (isset($diff['duration'])) {
            $diff['free_spin_duration'] = $this->handleDiff($prevFreeSpin?->duration, $currentFreeSpin->duration);
            unset($diff['duration']);
        }
        if ($prevFreeSpin?->bonus || $currentFreeSpin->bonus) {
            $diff += $this->getBonusDiff($prevFreeSpin?->bonus?->toArray() ?? [], $currentFreeSpin->bonus->toArray());
        }
        if (isset($diff['slot_id'])) {
            $diff['slot'] = $this->getSlotDiffData($prevFreeSpin?->slot_id, $currentFreeSpin->slot_id);
            unset($diff['slot_id']);
        }
        if (isset($diff['provider_id'])) {
            $diff['slot_provider'] = $this->getSlotProviderDiffData($prevFreeSpin?->provider_id, $currentFreeSpin->provider_id);
            unset($diff['provider_id']);
        }
        if ($prevFreeSpin?->options !== $currentFreeSpin->options) {
            $diff += $this->getOptionsDiffData($prevFreeSpin->options ?? [], $currentFreeSpin->options ?? []);
        }
        if (!$prevFreeSpin) {
            $diff['bonus_balance'] = $this->handleDiff(null, $currentFreeSpin->bonus !== null);
        }
        $this->createFreeSpinHistoryLog(
            $currentFreeSpin->id,
            $currentFreeSpin,
            $prevFreeSpin === null ? 'create' : 'update',
            $diff,
            $authorEmail
        );
    }

    private function getOptionsDiffData(array $prev, array $current): array
    {
        $data = [];
        $fields = ['mines_count', 'difficulty'];
        foreach ($fields as $field) {
            if (($prev[$field] ?? null) !== ($current[$field] ?? null)) {
                $diff = $this->handleDiff($prev[$field] ?? null, $current[$field] ?? null);
                if ($diff) {
                    $data[$field] = $diff;
                }
            }
        }

        return $data;
    }

    private function getSlotDiffData(?int $prev, ?int $current): array
    {
        $data = ['previous' => [], 'current' => []];
        if ($prev) {
            $prevSlotDto = $this->slotService->getSlotById($prev);
            $data['previous'] = [
                [
                    'id' => $prevSlotDto->id,
                    'name' => $prevSlotDto->name,
                ]
            ];
        }
        if ($current) {
            $currentSlotDto = $this->slotService->getSlotById($current);
            $data['current'] = [
                [
                    'id' => $currentSlotDto->id,
                    'name' => $currentSlotDto->name,
                ]
            ];
        }

        return $data;
    }

    private function getSlotProviderDiffData(?int $prev, ?int $current): array
    {
        $data = ['previous' => [], 'current' => []];
        if ($prev) {
            $prevSloProviders = $this->slotProviderService->getSlotProvidersByIds([$prev]);
            if (isset($prevSloProviders[$prev])) {
                $data['previous'] = [
                    [
                        'id' => $prevSloProviders[$prev]->id,
                        'name' => $prevSloProviders[$prev]->name,
                    ]
                ];
            }
        }
        if ($current) {
            $currentSloProviders = $this->slotProviderService->getSlotProvidersByIds([$current]);
            if (isset($currentSloProviders[$current])) {
                $data['current'] = [
                    [
                        'id' => $currentSloProviders[$current]->id,
                        'name' => $currentSloProviders[$current]->name,
                    ]
                ];
            }
        }

        return $data;
    }

    private function getBonusDiff(array $prev, array $current): array
    {
        $fields = ['wager', 'max_transfers', 'duration', 'min_bet', 'genre_id'];
        $data = [];
        foreach ($fields as $field) {
            if (isset($prev[$field]) || isset($current[$field])) {
                $diff = $this->handleDiff($prev[$field] ?? null, $current[$field] ?? null);
                if ($diff) {
                    $data[$field] = $diff;
                }
            }
        }

        if (isset($prev['slots']) || isset($current['slots'])) {
            $prevSlots = [];
            $currentSlots = [];

            if (isset($prev['slots'])) {
                /** @var Collection<int, SlotDto> $prevSlotsCollection */
                $prevSlotsCollection = $prev['slots'];
                $prevSlots = $this->prepareBonusSlotsCollection($prevSlotsCollection);
            }

            if (isset($current['slots'])) {
                /** @var Collection<int, SlotDto> $currentSlotsCollection */
                $currentSlotsCollection = $current['slots'];
                $currentSlots = $this->prepareBonusSlotsCollection($currentSlotsCollection);
            }

            $diffSlotsPrev = $this->getArrayDiffByEntityId($prevSlots, $currentSlots);
            $diffSlotsCurrent = $this->getArrayDiffByEntityId($currentSlots, $prevSlots);

            if (count($diffSlotsPrev) > 0 || count($diffSlotsCurrent) > 0) {
                $data['bonus_slots'] = [
                    'previous' => $diffSlotsPrev,
                    'current' => $diffSlotsCurrent,
                ];
            }
        }

        if (isset($prev['slotProviders']) || isset($current['slotProviders'])) {
            $prevSlotProviders = [];
            $currentSlotProviders = [];

            if (isset($prev['slotProviders'])) {
                /** @var Collection<int, SlotProviderDto> $prevSlotProvidersCollection */
                $prevSlotProvidersCollection = $prev['slotProviders'];
                $prevSlotProviders = $this->prepareBonusProvidersCollection($prevSlotProvidersCollection);
            }

            if (isset($current['slotProviders'])) {
                /** @var Collection<int, SlotProviderDto> $currentSlotProvidersCollection */
                $currentSlotProvidersCollection = $current['slotProviders'];
                $currentSlotProviders = $this->prepareBonusProvidersCollection($currentSlotProvidersCollection);
            }

            $diffProvidersPrev = $this->getArrayDiffByEntityId($prevSlotProviders, $currentSlotProviders);
            $diffProvidersCurrent = $this->getArrayDiffByEntityId($currentSlotProviders, $prevSlotProviders);

            if (count($diffProvidersPrev) > 0 || count($diffProvidersCurrent) > 0) {
                $data['bonus_slot_providers'] = [
                    'previous' => $diffProvidersPrev,
                    'current' => $diffProvidersCurrent,
                ];
            }
        }

        return $data;
    }

    private function prepareBonusSlotsCollection(Collection $collection): array
    {
        return $collection
            ->unique()
            ->map(static fn(SlotDto $slot) => ['id' => $slot->id, 'name' => $slot->name])
            ->values()
            ->toArray();
    }

    private function prepareBonusProvidersCollection(Collection $collection): array
    {
        return $collection
            ->unique()
            ->map(static fn(SlotProviderDto $slotProvider) => ['id' => $slotProvider->id, 'name' => $slotProvider->name])
            ->values()
            ->toArray();
    }

    private function getDiff(array $prev, array $current): array
    {
        $data = [];
        foreach (self::FIELDS as $field) {
            if (isset($prev[$field]) || isset($current[$field])) {
                $diff = $this->handleDiff($prev[$field] ?? null, $current[$field] ?? null);
                if ($diff) {
                    if ($field === 'start_at' || $field === 'expired_at') {
                        $data[$field] = [
                            'current' => $diff['current'] ? Carbon::parse($diff['current'])->timestamp : null,
                            'previous' => $diff['previous'] ? Carbon::parse($diff['previous'])->timestamp : null,
                        ];
                    } else {
                        $data[$field] = $diff;
                    }
                }
            }
        }

        return $data;
    }

    private function getArrayDiffByEntityId(array $prev, array $current): array
    {
        return array_values(
            array_udiff(
                $prev,
                $current,
                static fn(array $entity1, array $entity2) => $entity1['id'] <=> $entity2['id']
            )
        );
    }

    private function handleDiff(mixed $prev, mixed $current): array
    {
        if ($prev !== $current) {
            return ['previous' => $prev, 'current' => $current];
        }

        return [];
    }

    public function createFreeSpinHistoryLog(int $id, ?FreeSpin $freeSpin, string $type, array $diff, ?string $authorEmail): void
    {
        FreeSpinHistoryLog::on('mysql.bonus')->create([
            'type' => $type,
            'free_spin_id' => $id,
            'author_email' => $authorEmail ?? 'system',
            'data' => [
                'free_spin' => $freeSpin?->toArray() ?? [],
                'diff' => $diff,
            ],
        ]);
    }
}
