<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\BonusTypeEnum;
use App\Models\Bonus;
use App\Models\BonusInfo;
use App\Models\PlayerFirstBonus;
use Illuminate\Support\Facades\Cache;

class ActiveBonusesCacheService
{
    private const FIRST_PLAYER_ORGANIC_BONUS_CACHE_PREFIX = 'first_player_organic_bonus:';
    private const NON_ORGANIC_WELCOME_BONUS_CACHE_PREFIX = 'non_organic_welcome_bonus:';
    private const ORGANIC_WELCOME_BONUS_CACHE_PREFIX = 'organic_welcome_bonus:';
    private const ACTIVE_BONUS_INFO_NOT_FOR_SMARTICO_CACHE_PREFIX = 'active_bonus_info_not_for_smartico:';
    private const ACTIVE_BONUS_INFO_FOR_SMARTICO_CACHE_PREFIX = 'active_bonus_info_for_smartico:';

    public function getFirstPlayerOrganicBonus(string $playerUUID): ?PlayerFirstBonus
    {
        return Cache::remember(
            self::FIRST_PLAYER_ORGANIC_BONUS_CACHE_PREFIX . $playerUUID,
            86400,
            static fn() => PlayerFirstBonus::query()
                ->where('player_uuid', $playerUUID)
                ->where('is_from_link', true)
                ->first()
        );
    }

    /**
     * @return array<int>
     */
    public function getNonOrganicWelcomeBonusIds(int $bonusId): array
    {
        return Cache::remember(
            self::NON_ORGANIC_WELCOME_BONUS_CACHE_PREFIX . $bonusId,
            180,
            static fn() => Bonus::query()
                ->select(['id'])
                ->scopes(['isWelcomeGroupType'])
                ->where('id', $bonusId)->where('is_organic', false)
                ->pluck('id')->all()
        );
    }

    /**
     * @return array<int>
     */
    public function getOrganicWelcomeBonusIds(): array
    {
        return Cache::remember(
            self::ORGANIC_WELCOME_BONUS_CACHE_PREFIX . 'bonuses_welcome_organic',
            60,
            static fn() => Bonus::query()
                ->select('id')
                ->scopes(['isWelcomeGroupType'])
                ->where('is_organic', true)
                ->pluck('id')->all()
        );
    }

    /**
     * @return array<int>
     */
    public function getActiveBonusInfoNotForSmarticoIds(string $currency): array
    {
        return Cache::remember(
            self::ACTIVE_BONUS_INFO_NOT_FOR_SMARTICO_CACHE_PREFIX . $currency,
            60,
            static fn() => BonusInfo::on('mysql.replica')
                ->select('id')
                ->where('active', true)
                ->where('currency', $currency)
                ->where('is_for_smartico', false)
                ->pluck('id')->all()
        );
    }

    /**
     * @return array<int>
     */
    public function getActiveBonusInfoForSmarticoIds(string $currency): array
    {
        return Cache::remember(
            self::ACTIVE_BONUS_INFO_FOR_SMARTICO_CACHE_PREFIX . $currency,
            60,
            static fn() => BonusInfo::on('mysql.replica')
                ->select(['id'])
                ->where('active', true)
                ->where('currency', $currency)
                ->where('is_for_smartico', true)
                ->pluck('id')->all()
        );
    }
}
