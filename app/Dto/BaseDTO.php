<?php

namespace App\Dto;

use Illuminate\Support\Str;
use ReflectionClass;
use TypeError;

/**
 * Class BaseDTO
 *
 * To transfer structured data to the service
 *
 * Request -> Controller -> Service -> Handler
 */
abstract class BaseDTO
{
    /**
     * @phpstan-ignore-next-line
     */
    final public function __construct(array $parameters = [])
    {
        $class = new ReflectionClass(static::class);

        $properties = $class->getProperties();
        foreach ($properties as $reflectionProperty) {
            $property = $reflectionProperty->getName();
            $propertySnakeCase = Str::snake($property);

            try {
                $this->{$property} = $parameters[$propertySnakeCase] ?? null;
            } catch (TypeError $exception) {
                $this->{$property} = $class->getDefaultProperties()[$property] ?? $class->getDefaultProperties()[$propertySnakeCase];
            }
        }
    }

    /**
     * @return static
     *
     * @phpstan-ignore-next-line
     */
    public static function fromArray(array $data): self
    {
        return new static($data);
    }

    /**
     * @phpstan-ignore-next-line
     */
    public function toArray(bool $isCamelCase = true): array
    {
        $vars = get_object_vars($this);

        if ($isCamelCase) {
            return $vars;
        }
        $data = [];

        foreach ($vars as $key => $value) {
            $data[Str::snake($key)] = $value;
        }

        return $data;
    }
}
