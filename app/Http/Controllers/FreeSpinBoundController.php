<?php

namespace App\Http\Controllers;

use App\Exceptions\Exception;
use App\Http\Requests\FreeSpinAttachRequest;
use App\Http\Requests\FreeSpinCancelBoundRequest;
use App\Http\Requests\FreeSpinShowRequest;
use App\Http\Requests\GetFreeSpinBoundsRequest;
use App\Http\Requests\UpdateBoundRequest;
use App\Http\Resources\FreeSpinBoundDataResource;
use App\Http\Resources\FreeSpinBoundResource;
use App\Services\Dto\FreeSpinBoundSearchDTO;
use App\Services\Dto\GetFreeSpinBoundsDTO;
use App\Services\Dto\UpdateBoundDTO;
use App\Services\FreeSpinBoundService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * Class FreeSpinBoundController
 */
class FreeSpinBoundController extends Controller
{
    /**
     * FreeSpinsController constructor.
     */
    public function __construct(
        private readonly FreeSpinBoundService $freeSpinBoundService,
    ) {
    }

    public function getFreeSpinBounds(GetFreeSpinBoundsRequest $request): AnonymousResourceCollection
    {
        /** @var GetFreeSpinBoundsDTO $dto */
        $dto = GetFreeSpinBoundsDTO::fromArray($request->validated());
        $bounds = $this->freeSpinBoundService->searchFreeSpinBoundsWithoutPagination($dto);

        return FreeSpinBoundDataResource::collection($bounds);
    }

    public function updateBound(UpdateBoundRequest $request): FreeSpinBoundDataResource
    {
        /** @var UpdateBoundDTO $dto */
        $dto = UpdateBoundDTO::fromArray($request->validated());
        $bounds = $this->freeSpinBoundService->updateBound($dto);

        return FreeSpinBoundDataResource::make($bounds);
    }

    /**
     * @throws Exception|\Exception
     *
     * @return string[]
     */
    public function attach(FreeSpinAttachRequest $request): array
    {
        $response =  $this->freeSpinBoundService->attachFreeSpinToPlayer($request->bonus_id, $request->player_id);

        return [$response ? 'ok' : 'failed'];
    }

    /**
     * @throws Exception
     *
     * @return string[]
     */
    public function cancelBound(FreeSpinCancelBoundRequest $request): array
    {
        $response = $this->freeSpinBoundService->cancelFreeSpinBound((int)$request->id, (int)$request->attributes->get('playerId'));

        return [$response ? 'ok' : 'failed'];
    }

    public function playerMeFreeSpinsByStatus(Request $request, string $status): AnonymousResourceCollection
    {
        $freespin = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus($request->attributes->get('playerId'), $status);

        return FreeSpinBoundResource::collection($freespin);
    }

    public function playerBoundHistory(int $playerId): AnonymousResourceCollection
    {
        $freespin = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus($playerId, 'history');

        return FreeSpinBoundResource::collection($freespin);
    }

    public function playerBoundActive(int $playerId): AnonymousResourceCollection
    {
        $freespin = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus($playerId, 'active');

        return FreeSpinBoundResource::collection($freespin);
    }

    public function getFreeSpinBoundsByFreeSpinID(FreeSpinShowRequest $request): LengthAwarePaginator
    {
        $dto = FreeSpinBoundSearchDTO::fromArray($request->validated());

        return $this->freeSpinBoundService->getFreeSpinBoundList($dto);
    }
}
