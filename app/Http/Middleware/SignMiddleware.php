<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Deversin\Signature\Http\Middleware\SignatureMiddleware;
use Deversin\Signature\SignatureInterface;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\UnauthorizedException;

class SignMiddleware extends SignatureMiddleware
{
    private SignatureInterface $signature;

    public function __construct(
        SignatureInterface $signature
    ) {
        $this->signature = $signature;
        parent::__construct($signature);
    }

    public function handle(Request $request, Closure $next): mixed
    {
        $signatureFromHeader = $request->header(SignatureInterface::SIGNATURE_HEADER, '');
        if (app()->environment(['local', 'dev', 'staging']) && !$signatureFromHeader) {
            return $next($request);
        }

        $verify = $this->signature
            ->verify(
                $request->post(),
                $request->header(SignatureInterface::SIGNATURE_HEADER, '')
            );

        if (!$verify) {
            throw new UnauthorizedException('Invalid signature');
        }

        /** @var Response $response */
        $response = $next($request);

        $response->headers->set(SignatureInterface::SIGNATURE_HEADER, $this->signature->sign($response->getContent()));

        return $response;
    }
}
