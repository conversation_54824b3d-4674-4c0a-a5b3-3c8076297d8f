<?php

namespace App\Http\Resources;

use App\Models\FreeSpinBound;
use App\Models\Traits\ImageLinkFixerTrait;
use App\Services\Dto\SlotDto;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin FreeSpinBound
 *
 * @property-read SlotDto $slot
 */
class FreeSpinBoundResource extends JsonResource
{
    use ImageLinkFixerTrait;

    /**
     * @param mixed $request
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'free_spin_id' => $this->free_spin_id,
            'bonus_id' => $this->bonus_id,
            'uuid' => $this->uuid,
            'bet' => $this->bet,
            'total_win' => $this->total_win,
            'count_left' => $this->count_left,
            'is_active' => $this->is_active,
            'status' => $this->status,
            'start_at' => !empty($this->start_at) ? Carbon::parse($this->start_at)->timestamp : null,
            'expire_at' => !empty($this->expire_at) ? Carbon::parse($this->expire_at)->timestamp : null,
            'canceled_at' => !empty($this->canceled_at) ? Carbon::parse($this->canceled_at)->timestamp : null,
            'created_at' => !empty($this->created_at) ? Carbon::parse($this->created_at)->timestamp : null,
            'updated_at' => !empty($this->updated_at) ? Carbon::parse($this->updated_at)->timestamp : null,
            'freespin' => FreeSpinDataResource::make($this->freespinBonusSlotWithTrashed),
            'slot' => !empty($this->slot) ? [
                'id' => $this->slot->id,
                'name' => $this->slot->name,
                'image' => $this->getImageAttribute($this->slot->image),
                'link_name' => $this->slot->linkName,
                'external_id' => $this->slot->externalId,
            ] : [],
            'bonus' => !empty($this->freespinBonusSlotWithTrashed)
                ? BonusDataResource::make($this->freespinBonusSlotWithTrashed->bonus) : null,
        ];
    }
}
