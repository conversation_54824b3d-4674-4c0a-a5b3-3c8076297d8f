<?php

namespace App\Http\Resources;

use App\Models\FreeSpinHistoryLog;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FreeSpinHistoryLogResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /** @var FreeSpinHistoryLog $resource */
        $resource = $this->resource;

        return [
            'id' => $resource->id,
            'type' => $resource->type,
            'author_email' => $resource->author_email,
            'data' => $resource->diff ? json_decode($resource->diff) : null,
            'created_at' => $resource->created_at,
        ];
    }
}
