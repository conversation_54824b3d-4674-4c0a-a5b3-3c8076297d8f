<?php

namespace App\Http\Resources;

use App\Models\BonusHistoryLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BonusHistoryLogResource extends JsonResource
{
    /**
     * @return array<string, null|string|int|Carbon|array<string, array<string, null|string|int|bool>>>
     */
    public function toArray(Request $request): array
    {
        /** @var BonusHistoryLog $resource */
        $resource = $this->resource;

        return [
            'id' => $resource->id,
            'type' => $resource->type,
            'author_email' => $resource->author_email,
            'data' => $resource->diff ? json_decode($resource->diff) : null,
            'created_at' => $resource->created_at,
        ];
    }
}
