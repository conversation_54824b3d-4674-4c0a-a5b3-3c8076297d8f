<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\Bonus;
use App\Models\Traits\ImageLinkFixerTrait;
use App\Traits\MoneyWithCurrencyTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BonusActiveWelcomeResource extends JsonResource
{
    use ImageLinkFixerTrait;
    use MoneyWithCurrencyTrait;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Bonus $resource */
        $resource = $this->resource;

        return [
            'id' => $resource->id,
            'name' => $resource->name,
            'bonus_name' => $resource->bonus_name,
            'image' => $this->getImageAttribute($resource->image),
            'crash' => $resource->crash,
            'max_bonus' => $this->geMoneyWithCurrency($resource->bonuses[0] ?? null, $resource->currency),
            'currency' => $resource->currency,
            'casino' => $resource->casino,
            'bets' => $resource->bets,
        ];
    }
}
