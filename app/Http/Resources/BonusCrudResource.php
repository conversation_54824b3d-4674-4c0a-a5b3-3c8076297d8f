<?php

namespace App\Http\Resources;

use App\Models\Bonus;
use App\Models\Traits\ImageLinkFixerTrait;
use App\Services\Dto\SlotDto;
use App\Services\Dto\SlotProviderDto;
use App\Traits\MoneyWithCurrencyTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BonusCrudResource extends JsonResource
{
    use ImageLinkFixerTrait;
    use MoneyWithCurrencyTrait;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Bonus $resource */
        $resource = $this->resource;

        return [
            'id' => $resource->id,
            'author_email' => $resource->author_email,
            'weight' => $resource->weight,
            'name' => $resource->name,
            'bonus_name' => $resource->bonus_name,
            'description' => $resource->description,
            'condition' => $resource->condition,
            'type' => $resource->type,
            'image' => $resource->image,
            'max_bonuses' => !$resource->is_free_spin_for_deposit ? $resource->bonuses : null,
            'max_bonus' => $this->geMoneyWithCurrency($resource->bonuses[0] ?? null, $resource->currency),
            'max_transfers' => $resource->max_transfers,
            'deposit_factors' => $resource->deposit_factors,
            'currency' => $resource->currency,
            'wager' => $resource->wager,
            'min_deposit' => $resource->min_deposit,
            'min_factor' => $resource->min_factor,
            'min_bet' => $resource->min_bet,
            'active' => $resource->active,
            'casino' => $resource->casino,
            'bets' => $resource->bets,
            'crash' => $resource->crash,
            'total_transferred' => $resource->total_transferred,
            'uses' => $resource->uses,
            'transfers' => $resource->transfers,
            'total_uses' => $resource->total_uses,
            'is_external' => $resource->is_external,
            'is_promo' => $resource->is_promo,
            'from' => !empty($resource->active_from) ? $resource->active_from->getTimestamp() : null,
            'to' => !empty($resource->active_til) ? $resource->active_til->getTimestamp() : null,
            'duration' => $resource->duration,
            'bonus_data' => $resource->data,
            'created_at' => !empty($resource->created_at) ? $resource->created_at->getTimestamp() : null,
            'updated_at' => !empty($resource->updated_at) ? $resource->updated_at->getTimestamp() : null,
            'is_welcome' => $resource->is_welcome_group,
            'is_onetime' => $resource->is_onetime_group,
            'is_no_dep' => $resource->is_no_dep,
            'is_deposit' => $resource->is_deposit,
            'is_free_spin_for_deposit' => $resource->is_free_spin_for_deposit,
            'slot_providers' => $resource->getAttribute('slotProviders')
                ?->map(static fn(SlotProviderDto $slotProvider): array => [
                    'id' => $slotProvider->id,
                    'count' => $slotProvider->count,
                    'name' => $slotProvider->name,
                    'custom_name' => $slotProvider->customName,
                    'is_bonus_barred' => $slotProvider->isBonusBarred,
                ]) ?? [],
            'slots' => $resource->getAttribute('slots')
                ?->map(static fn(SlotDto $slot): array => [
                    'id' => $slot->id,
                    'name' => $slot->name,
                    'external_id' => $slot->externalId,
                ]) ?? [],
            'trigger_sessions' => $resource->triggerSessions->isNotEmpty()
                ? TriggerSessionsDataResource::collection($resource->triggerSessions)
                : null,
            'freespin' => $resource->freespin ? FreeSpinDataResource::make($resource->freespin) : null,
            'max_real_balance' => $resource->max_real_balance,
            'is_organic' => $resource->is_organic,
            'genre_id' => $resource->genre_id,
        ];
    }
}
