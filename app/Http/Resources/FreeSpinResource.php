<?php

namespace App\Http\Resources;

use App\Models\FreeSpin;
use App\Services\Dto\SlotDto;
use App\Services\Dto\SlotProviderDto;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property SlotDto $slot
 */
class FreeSpinResource extends JsonResource
{
    /**
     * @param mixed $request
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var FreeSpin $resource */
        $resource = $this->resource;
        $bonus = null;
        if ($resource->bonus_id) {
            $bonus = $resource->bonus;
        }

        $options = $resource->options ?? [];

        return [
            'id' => $resource->id,
            'author_email' => $resource->author_email,
            'supplier' => $resource->supplier,
            'aggregator' => $resource->aggregator,
            'slots_id' => $resource->slot_id,
            'slot_external_id' => $resource->slot_external_id,
            'type' => $resource->type,
            'provider_id' => $resource->provider_id,
            'name' => $resource->name,
            'title' => $resource->title,
            'count' => $resource->count,
            'duration' => $bonus?->duration,
            'free_spin_duration' => $resource->duration,
            'min_bet' => $bonus?->min_bet,
            'max_transfer' => $bonus?->max_transfers,
            'wager' => $bonus?->wager,
            'bet' => $resource->bet,
            'bet_id' => $resource->bet_id,
            'bonus_id' => $resource->bonus_id,
            'denomination' => $resource->denomination,
            'currency' => $resource->currency,
            'status' => $resource->status,
            'is_active' => $resource->is_active,
            'author_id' => $resource->author_id,
            'updated_by_author_id' => $resource->updated_by_author_id,
            'in_process' => $resource->in_process,
            'data' => $resource->data,
            'start_at' => $resource->start_at,
            'expired_at' => $resource->expired_at,
            'created_at' => $resource->created_at,
            'updated_at' => $resource->updated_at,
            'slot' => [
                'id' => $resource->slot_id,
                'name' => $this->slot->name ?? '',
                'external_id' => $this->slot->externalId ?? '',
            ],
            'provider' => [
                'id' => $resource->provider_id,
                'name' => $this->slot->provider ?? '',
            ],
            ...$options,
            'bonus_genre_id' => $bonus?->genre_id,
            'bonus_slot_providers' => $bonus?->getAttribute('slotProviders')
                ?->map(fn(SlotProviderDto $slotProvider): array => [
                    'id' => $slotProvider->id,
                    'count' => $slotProvider->count,
                    'name' => $slotProvider->name,
                    'custom_name' => $slotProvider->customName,
                    'is_bonus_barred' => $slotProvider->isBonusBarred,
                ]) ?? [],
            'bonus_slots' => $bonus?->getAttribute('slots')
                ?->map(fn(SlotDto $slot): array => [
                    'id' => $slot->id,
                    'name' => $slot->name,
                    'external_id' => $slot->externalId,
                ]) ?? [],

        ];
    }
}
