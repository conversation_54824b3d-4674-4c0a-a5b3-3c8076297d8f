<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\IsFreeSpinIssuedRule;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ConditionalRules;
use Illuminate\Validation\Rules\ExcludeIf;
use Illuminate\Validation\Rules\RequiredIf;
use Illuminate\Validation\Rules\Unique;

/**
 * Class FreeSpinUpdateRequest
 *
 * @property int $id
 * @property string $name
 * @property int $updated_by_author_id
 * @property string $supplier
 * @property null|string $aggregator
 * @property int $slots_id
 * @property null|string $slot_external_id
 * @property int $provider_id
 * @property string $provider_name
 * @property string $name
 * @property string $title
 * @property null|int $wager
 * @property int $count
 * @property bool $bonus_balance
 * @property null|bool $is_active
 * @property null|int $bet
 * @property string $currency
 * @property string $status
 * @property null|string $bet_id
 * @property null|int $denomination
 * @property null|string $start_at
 * @property string $expired_at
 * @property array<string, string> $players
 * @property null|int $duration
 * @property null|int $max_transfer
 * @property null|int $min_bet
 */
class FreeSpinUpdateRequest extends FreeSpinCreateRequest
{
    /**
     * @return array<string, array<ConditionalRules|ExcludeIf|RequiredIf|Unique|string>>
     */
    public function rules(): array
    {
        $parentRules = parent::rules();

        $this->merge([
            'id' => $this->route('id'),
        ]);

        $rules = collect($parentRules)->except('author_id')->toArray();

        /**
         * @var array<string, array<ConditionalRules|ExcludeIf|RequiredIf|Unique|string>> $mergedRules
         */
        $mergedRules = array_merge($rules, [
            'id' => [
                'required',
                'numeric',
                'exists:free_spins',
                Rule::when($this->isGr8Tech(), [new IsFreeSpinIssuedRule()])],
            'name' => [
                'required',
                'string',
                Rule::unique('free_spins', 'name')->ignore($this->route('id'))->whereNull('deleted_at'),
            ],
            'updated_by_author_id' => ['required', 'numeric', 'gt:0'],
        ]);

        return $mergedRules;
    }
}
