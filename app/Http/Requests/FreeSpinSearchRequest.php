<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class FreeSpinSearchRequest
 *
 * @property null|bool $option
 * @property null|int $id
 * @property null|string $type
 * @property null|string $name
 * @property null|int $player_id_with
 * @property null|string $currency
 * @property null|string $supplier
 * @property null|string $aggregator
 * @property null|int $provider_id
 * @property null|int $slot_id
 * @property null|int $bonus_id
 * @property null|int $updated_by_author_id
 * @property null|int $author_id
 * @property null|int $count_from
 * @property null|int $count_to
 * @property null|int $bet_from
 * @property null|int $bet_to
 * @property null|string $start_at_from
 * @property null|string $start_at_to
 * @property null|string $expired_at_from
 * @property null|string $expired_at_to
 * @property null|string $created_at_from
 * @property null|string $created_at_to
 * @property null|string $updated_at_from
 * @property null|string $updated_at_to
 */
class FreeSpinSearchRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'option' => 'nullable|boolean',
            'id' => 'nullable|numeric',
            'type' => 'nullable|string',
            'name' => 'nullable|string',
            'title' => 'nullable|string',
            'player_id_with' => 'nullable|numeric',
            'currency' => 'nullable|string',
            'supplier' => 'nullable|string',
            'aggregator' => 'nullable|string',
            'provider_id' => 'nullable|numeric',
            'slot_id' => 'nullable|numeric',
            'bonus_id' => 'nullable|numeric',
            'updated_by_author_id' => 'nullable|numeric',
            'author_id' => 'nullable|numeric',
            'count_from' => 'nullable|numeric',
            'count_to' => 'nullable|numeric',
            'bet_from' => 'nullable|numeric',
            'bet_to' => 'nullable|numeric',
            'start_at_from' => 'nullable|date',
            'start_at_to' => 'nullable|date',
            'expired_at_from' => 'nullable|date',
            'expired_at_to' => 'nullable|date',
            'created_at_from' => 'nullable|date',
            'created_at_to' => 'nullable|date',
            'updated_at_from' => 'nullable|date',
            'updated_at_to' => 'nullable|date',
            ...$this->paginationRules(),
        ];
    }
}
