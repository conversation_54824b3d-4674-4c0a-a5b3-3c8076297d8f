<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class CheckFreeSpinRequest
 *
 * @property int $id
 * @property string $name
 */
class CheckFreeSpinRequest extends BaseRequest
{
    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'id' => 'numeric|exists:free_spins',
            'name' => 'required|string',
        ];
    }
}
