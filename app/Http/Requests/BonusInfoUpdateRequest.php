<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class BonusInfoUpdateRequest
 *
 * @property int $id
 */
class BonusInfoUpdateRequest extends BonusInfoCreateRequest
{
    public function authorize(): bool
    {
        $this->merge([
            'id' => $this->route('id'),
        ]);

        return true;
    }

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'id' => ['required', 'integer', 'exists:bonus_info,id'],
        ]);
    }
}
