<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class FreeSpinResendRequest
 *
 * @property string $freebet_id
 * @property int $amount
 */
class FreeSpinFinishRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'freebet_id' => ['required', 'string'],
            'amount' => ['required', 'numeric', 'min:0'],
        ];
    }
}
