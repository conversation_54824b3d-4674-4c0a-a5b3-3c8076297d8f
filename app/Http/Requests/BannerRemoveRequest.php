<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class BannerRemoveRequest
 *
 * @property int $id
 */
class BannerRemoveRequest extends BaseRequest
{
    public function authorize(): bool
    {
        /** @var int|string $id */
        $id = $this->route('id');

        $this->merge([
            'id' => (int)$id,
        ]);

        return true;
    }

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'numeric', 'exists:banners'],
        ];
    }
}
