<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class GetBonusRelatedToSlotRequest
 *
 * @property int $bonus_id
 * @property int $slot_id
 */
class GetBonusRelatedToSlotRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'bonus_id' => ['required', 'int', 'exists:bonuses,id'],
            'slot_id' => ['required', 'int', 'min:1'],
        ];
    }
}
