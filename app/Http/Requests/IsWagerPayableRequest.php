<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * @property int $bonus_id
 * @property int $slot_id
 * @property int $external_provider_id
 * @property bool $is_bonus_ready
 */
class IsWagerPayableRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'bonus_id' => ['required', 'integer', 'min:1'],
            'slot_id' => ['required', 'integer', 'min:1'],
            'external_provider_id' => ['required', 'integer', 'min:1'],
            'is_bonus_ready' => ['required', 'boolean'],
        ];
    }
}
