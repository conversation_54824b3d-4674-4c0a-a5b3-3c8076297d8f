<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class PromoCodeViewRequest
 *
 * @property int $id
 * @property bool $welcome
 * @property bool $no_dep
 * @property null|string $code
 */
class PromoCodeViewRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'id' => ['integer', 'min:1', 'exists:promocodes,id'],
            'welcome' => ['boolean'],
            'no_dep' => ['boolean'],
            'code' => ['nullable', 'string', 'max:255'],
            ...$this->paginationRules(),
        ];
    }
}
