<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * Class BaseRequest
 */
class BaseRequest extends FormRequest
{
    protected function failedValidation(Validator $validator): void
    {
        /**
         * @var ResponseFactory $response
         */
        $response = response();

        throw new HttpResponseException($response->json([
            'errors' => $validator->errors(),
            'status' => 422,
        ], 422));
    }
}
