<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class UpdateBoundRequest
 *
 * @property int $id
 * @property null|int $count_left
 * @property null|int $total_win
 * @property null|bool $is_archived
 * @property null|bool $is_active
 */
class UpdateBoundRequest extends BaseRequest
{
    public function authorize(): bool
    {
        /** @var int|string $id */
        $id = $this->route('id');

        $this->merge([
            'id' => (int)$id,
        ]);

        return true;
    }

    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer|exists:free_spin_bounds,id',
            'count_left' => 'nullable|integer',
            'total_win' => 'nullable|integer',
            'is_archived' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
        ];
    }
}
