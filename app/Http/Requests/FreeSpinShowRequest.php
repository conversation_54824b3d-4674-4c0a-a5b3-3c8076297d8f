<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class FreeSpinShowRequest
 *
 * @property int $id
 * @property null|string $player_uuid
 */
class FreeSpinShowRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        if (!$this->has('id') && $this->route('id')) {
            $this->merge([
                'id' => $this->route('id'),
            ]);
        }

        return [
            'id' => ['required', 'numeric', 'exists:free_spins'],
            'player_uuid' => ['nullable', 'string', 'uuid'],
            ...$this->paginationRules(),
        ];
    }
}
