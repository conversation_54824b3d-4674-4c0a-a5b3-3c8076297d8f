<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class FreeSpinResendRequest
 *
 * @property int $freespin_id
 * @property null|int $freespin_bound_id
 */
class FreeSpinResendRequest extends BaseRequest
{
    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'freespin_id' => 'required|numeric',
            'freespin_bound_id' => 'nullable|numeric',
        ];
    }
}
