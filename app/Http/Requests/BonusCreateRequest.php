<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\BonusTypeEnum;
use App\Enums\SlotGenreEnum;
use App\Services\BonusService;
use Illuminate\Validation\Rule;

/**
 * Class BonusCreateRequest
 *
 * @property null|bool $bonus_balance
 * @property null|bool $is_active
 * @property string $name
 * @property string $type
 * @property null|int $count
 * @property null|float $bet
 * @property null|string $bet_id
 * @property null|float $denomination
 * @property null|int $author_id
 * @property null|string[] $description
 * @property null|string[] $condition
 * @property null|string[] $bonus_name
 * @property null|int $min_bet
 * @property null|int $min_deposit
 * @property null|int[] $max_transfers
 * @property null|int[] $max_bonuses
 * @property null|int[] $deposit_factors
 * @property null|int $duration
 * @property null|float $wager
 * @property null|string $currency
 * @property null|bool $active
 * @property null|bool $is_promo
 * @property null|bool $is_external
 * @property null|bool $casino
 * @property null|bool $bets
 * @property null|int $from
 * @property null|int $to
 * @property null|float $min_factor
 * @property null|array<string, string> $data
 * @property null|int[] $slot_providers
 * @property null|int[] $slots
 * @property null|array<string, string> $trigger_sessions
 * @property null|int $segment_id
 * @property null|string $image
 * @property null|int $max_real_balance
 * @property bool $is_organic
 * @property null|int $genre_id
 */
class BonusCreateRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(BonusService $bonusService): array
    {
        $rules = [
            'bonus_balance' => ['required_if:type,free_spins_for_deposit', 'boolean'],
            'is_active' => ['nullable', 'boolean'],
            'name' => ['required', 'string'],
            'type' => ['required', 'string', Rule::in(BonusTypeEnum::values())],
            'count' => ['required_if:type,free_spins_for_deposit', 'numeric'],
            'bet' => ['required_if:type,free_spins_for_deposit', 'numeric'],
            'bet_id' => ['required_if:type,free_spins_for_deposit', 'string'],
            'denomination' => ['required_if:type,free_spins_for_deposit', 'numeric'],
            'author_email' => ['required_if:type,free_spins_for_deposit', 'string'],
            'author_id' => ['required_if:type,free_spins_for_deposit', 'numeric'],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string'],
            'condition' => ['nullable', 'array'],
            'condition.*' => ['nullable', 'string'],
            'bonus_name' => ['nullable', 'array'],
            'bonus_name.*' => ['nullable', 'string'],
            'min_bet' => ['nullable', 'integer', 'gt:0'],
            'min_deposit' => ['nullable', 'integer', 'gt:0'],
            'max_transfers' => ['sometimes', 'array'],
            'max_transfers.*' => ['numeric', 'sometimes', 'gt:0'],
            'max_bonuses' => ['sometimes', 'array'],
            'max_bonuses.*' => ['numeric', 'sometimes', 'gt:0'],
            'deposit_factors' => ['sometimes', 'array'],
            'deposit_factors.*' => ['numeric', 'sometimes', 'gt:0'],
            'duration' => ['nullable', 'integer', 'gt:0'],
            'wager' => ['nullable', 'numeric', 'gt:0.0'],
            'currency' => ['nullable', 'string', 'between:3,3'],
            'active' => ['nullable', 'boolean'],
            'is_promo' => ['nullable', 'boolean'],
            'is_external' => ['nullable', 'boolean'],
            'casino' => ['nullable', 'boolean'],
            'bets' => ['nullable', 'boolean'],
            'from' => ['nullable', 'date_format:U'],
            'to' => ['nullable', 'date_format:U'],
            'min_factor' => ['nullable', 'numeric', 'gt:1.0'],
            'data' => ['nullable', 'array'],
            'data.tournament_name' => ['nullable', 'string'],
            'data.win_type' => ['nullable', Rule::in(['bonus', 'real'])],
            'data.sport_name' => ['nullable', 'string'],
            'slot_providers' => ['nullable', 'array'],
            'slot_providers.*' => ['numeric'],
            'slots' => ['nullable', 'array'],
            'slots.*' => ['nullable', 'numeric'],
            'trigger_sessions' => ['required_if:type,free_spins_for_deposit', 'array'],
            'trigger_sessions.*.start_at' => ['required_if:type,free_spins_for_deposit', 'date_format:U'],
            'trigger_sessions.*.end_at' => ['required_if:type,free_spins_for_deposit', 'date_format:U'],
            'segment_id' => ['nullable', 'integer'],
            'image' => ['nullable', 'url'],
            'max_real_balance' => ['nullable', 'integer', 'min:1', 'max:99999900'],
            'is_organic' => ['boolean'],
            'genre_id' => ['nullable', Rule::enum(SlotGenreEnum::class)],
            'free_spin_slot_provider' => ['required_if:type,free_spins_for_deposit', 'numeric'],
            'free_spin_slot' => ['required_if:type,free_spins_for_deposit', 'numeric'],
        ];

        if (in_array($this->input('type'), [BonusTypeEnum::WELCOME->value, BonusTypeEnum::WELCOME_4_STEPS->value], true)) {
            $rules['weight'][] = ['required', 'int', 'min:1', 'max:999'];
        }

        if ($this->method() === 'PUT') {
            /**
             * @var int|string $id
             */
            $id = $this->route('id');

            $this->merge(['id' => $id]);

            $rules['id'] = ['required', 'int', 'exists:bonuses'];

            $rules['type'][] = Rule::exists('bonuses', 'type')->where('id', $id);

            $bonus = $bonusService->getActualBonusById((int)$id);
            $rules['max_real_balance'] = [Rule::when(
                (($bonus->uses ?? 0) > 0) && $bonus?->max_real_balance !== $this->max_real_balance,
                ['required', Rule::in([$bonus?->max_real_balance])],
                ['nullable', 'integer', 'min:1', 'max:99999900']
            )];
        }

        return $rules;
    }
}
