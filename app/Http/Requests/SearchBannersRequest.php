<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;
use App\ValueObjects\BannerTypeEnum;
use Illuminate\Validation\Rule;

/**
 * Class SearchBannersRequest
 *
 * @property int $id
 * @property string $lang
 * @property string $country
 * @property string $name
 * @property string $url
 * @property bool $enabled
 * @property string $type
 * @property string $dispositions
 */
class SearchBannersRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'id' => 'numeric',
            'lang' => 'string|max:3',
            'country' => 'string|max:3',
            'name' => 'string|max:255',
            'url' => 'string',
            'enabled' => 'boolean',
            'type' => ['string', Rule::in(
                [BannerTypeEnum::TYPE_BIG, BannerTypeEnum::TYPE_SMALL, BannerTypeEnum::TYPE_UPPER]
            )],
            'dispositions' => ['string'],
            ...$this->paginationRules(),
        ];
    }
}
