<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class BannerEnabledRequest
 *
 * @property null|string $country
 */
class BannerEnabledRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'country' => ['nullable', 'string', 'max:3'],
            ...$this->paginationRules(),
        ];
    }
}
