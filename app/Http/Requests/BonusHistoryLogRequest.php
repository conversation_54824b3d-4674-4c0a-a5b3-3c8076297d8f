<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class BonusHistoryLogRequest
 *
 * @property int $id
 */
class BonusHistoryLogRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    public function authorize(): bool
    {
        $this->merge([
            'id' => $this->route('bonus'),
        ]);

        return true;
    }

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'numeric', 'exists:bonuses,id'],
            ...$this->paginationRules(),
        ];
    }
}
