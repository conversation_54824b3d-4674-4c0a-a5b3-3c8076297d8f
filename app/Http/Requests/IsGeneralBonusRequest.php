<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * @property int $bonus_id
 */
class IsGeneralBonusRequest extends BaseRequest
{
    public function authorize(): bool
    {
        $this->merge([
            'bonus_id' => (int)$this->route('bonus_id'),
        ]);

        return true;
    }

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'bonus_id' => ['required', 'integer', 'min:1', 'exists:bonuses,id'],
        ];
    }
}
