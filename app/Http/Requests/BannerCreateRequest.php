<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\CheckBannerExpirationRule;
use App\ValueObjects\BannerPlatformEnum;
use App\ValueObjects\BannerSignedEnum;
use App\ValueObjects\BannerTypeEnum;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

/**
 * Class BannerCreateRequest
 *
 * @property int $client_id
 * @property string $name
 * @property null|string $language
 * @property null|string $country
 * @property string $is_for_signed_in
 * @property null|string $location
 * @property null|int $weight
 * @property bool $mobile_apk_install
 * @property string $platform
 * @property array<int, mixed> $disposition
 * @property null|string $image
 * @property string $type
 * @property bool $enabled
 * @property null|string $smartico_segment_id
 * @property null|int $start_at
 * @property null|int $end_at
 * @property null|int $id
 */
class BannerCreateRequest extends BaseRequest
{
    /**
     * @return array<string, array<CheckBannerExpirationRule|In|string>>
     */
    public function rules(): array
    {
        $rules = [
            'client_id' => ['numeric'],
            'name' => ['required', 'string'],
            'language' => ['nullable', 'string', 'max:2'],
            'country' => ['nullable', 'string', 'max:3'],
            'is_for_signed_in' => ['required', 'string', Rule::in(BannerSignedEnum::SIGNED_TYPES)],
            'location' => ['nullable', 'string'],
            'weight' => ['nullable', 'int'],
            'mobile_apk_install' => ['required', 'boolean'],
            'platform' => ['required', 'string', Rule::in(BannerPlatformEnum::ALL_DEVICES)],
            'disposition' => ['array'],
            'image' => ['nullable', 'string', 'url'],
            'type' => ['required', 'string', Rule::in(
                [BannerTypeEnum::TYPE_BIG, BannerTypeEnum::TYPE_SMALL, BannerTypeEnum::TYPE_UPPER]
            )],
            'enabled' => ['required', 'boolean'],
            'smartico_segment_id' => ['nullable', 'string'],
            'start_at' => ['nullable', 'date_format:U'],
            'end_at' => ['nullable', 'date_format:U'],
        ];

        if ($this->method() === 'PUT') {
            $this->merge([
                'id' => $this->route('id'),
            ]);

            /**
             * @var int $endAt
             */
            $endAt = $this->get('end_at');

            $rules['id'] = [
                'required',
                'int',
                'exists:banners',
                'bail',
                new CheckBannerExpirationRule($endAt),
            ];
        }

        return $rules;
    }
}
