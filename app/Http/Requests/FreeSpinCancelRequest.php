<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class FreeSpinCancelRequest
 *
 * @property int $id
 * @property string $author_email
 */
class FreeSpinCancelRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        $this->merge([
            'id' => $this->route('id'),
        ]);

        return [
            'id' => [
                'required',
                'numeric',
                'exists:free_spins,id',
            ],
            'author_email' => ['required', 'string'],
        ];
    }
}
