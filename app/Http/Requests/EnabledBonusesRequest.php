<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class EnabledBonusesRequest
 *
 * @property null|int $id
 * @property null|string $currency
 * @property null|string $type
 */
class EnabledBonusesRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'id' => ['nullable', 'numeric'],
            'currency' => ['nullable', 'between:3,3'],
            'type' => ['nullable', 'string'],
            ...$this->paginationRules(),
        ];
    }
}
