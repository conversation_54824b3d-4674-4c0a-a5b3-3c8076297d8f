<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\CheckPlayerPromocodeUnavailability;
use App\Rules\CheckPromocodeIsWelcome;
use App\Rules\MatchPromoCodeBonusAndPlayerCurrency;
use App\Rules\PromoCodeValidation;
use App\Services\PlayerService;
use App\Services\PromoCodeService;

/**
 * Class PromoCodeApplyRequest
 *
 * @property int $player_id
 * @property string $code
 */
class PromoCodeApplyRequest extends BaseRequest
{
    /**
     * @return array<string, array<PromoCodeValidation|CheckPromocodeIsWelcome|MatchPromoCodeBonusAndPlayerCurrency|CheckPlayerPromocodeUnavailability|string>>
     */
    public function rules(PlayerService $playerService, PromoCodeService $promoCodeService): array
    {
        /**
         * @var int $playerId
         */
        $playerId = $this->attributes->get('playerId');

        $player = $playerService->getPlayerByID($playerId);
        $promoCode = $promoCodeService->getPromoCode($this->code);
        $promoCodeIds = $promoCodeService->getPromoCodeIdsByPlayerId($player->id);

        $this->merge(['player_id' => $playerId]);

        return [
            'player_id' => ['bail', 'required', 'int', 'gt:0'],
            'code' => [
                'bail',
                'required',
                'string',
                'max:255',
                'exists:promocodes,code',
                new PromoCodeValidation($promoCode),
                new CheckPromocodeIsWelcome($promoCode),
                new MatchPromoCodeBonusAndPlayerCurrency($player, $promoCode),
                new CheckPlayerPromocodeUnavailability($promoCode, $promoCodeIds),
            ],
        ];
    }
}
