<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\AggregatorEnum;
use App\Enums\GameDifficultyEnum;
use App\Enums\InoutGamesEnum;
use App\Enums\ProviderEnum;
use App\Enums\SlotGenreEnum;
use App\Enums\SupplierEnum;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ConditionalRules;
use Illuminate\Validation\Rules\ExcludeIf;
use Illuminate\Validation\Rules\RequiredIf;
use Illuminate\Validation\Rules\Unique;
use Illuminate\Validation\Rules\Enum;
/**
 * Class FreeSpinCreateRequest
 *
 * @property string $supplier
 * @property null|string $aggregator
 * @property int $slots_id
 * @property null|string $slot_external_id
 * @property int $provider_id
 * @property string $provider_name
 * @property string $name
 * @property string $title
 * @property null|int $wager
 * @property int $count
 * @property bool $bonus_balance
 * @property null|bool $is_active
 * @property null|int $bet
 * @property string $currency
 * @property string $status
 * @property null|string $bet_id
 * @property int $author_id
 * @property null|int $denomination
 * @property null|string $start_at
 * @property string $expired_at
 * @property array<string, string> $players
 * @property null|int $duration
 * @property null|int $max_transfer
 * @property null|int $min_bet
 * @property null|string $inout_game_name
 * @property null|int $mines_count
 * @property null|string $difficulty
 * @property null|int[] $bonus_genre_id
 * @property null|int[] $bonus_slot_providers
 * @property null|int[] $bonus_slots

 */
class FreeSpinCreateRequest extends BaseRequest
{
    /**
     * @return array<string, list<ConditionalRules|Enum|ExcludeIf|RequiredIf|Unique|string>>
     */
    public function rules(): array
    {
        return [
            'supplier' => ['required', 'string'],
            'aggregator' => [
                'nullable',
                'string',
                Rule::requiredIf(
                    fn() => $this->string('supplier')->lower()->toString() === SupplierEnum::GAMICORP_INTEGRATION->value
                ),
                Rule::excludeIf(
                    fn() => $this->string('supplier')->lower()->toString() !== SupplierEnum::GAMICORP_INTEGRATION->value
                ),
            ],
            'slots_id' => ['required', 'numeric'],
            'slot_external_id' => ['nullable', 'string'],
            'provider_id' => ['required', 'numeric'],
            'name' => [
                'required',
                'string',
                Rule::unique('free_spins', 'name')->whereNull('deleted_at'),
            ],
            'title' => ['required', 'string', 'min:1', 'max:50'],
            'wager' => ['nullable', 'numeric', 'gt:0.0'],
            'count' => ['required', 'numeric'],
            'bonus_balance' => [
                'required',
                'boolean',
                Rule::when($this->isGr8Tech(), [Rule::in([true])]),
            ],
            'is_active' => ['nullable', 'boolean'],
            'bet' => [
                'nullable',
                'numeric',
                Rule::requiredIf(
                    fn() => $this->string('aggregator')->lower()->toString() !== AggregatorEnum::SOFTSWISS->value
                ),
                Rule::when($this->isGr8Tech(), [
                    'required',
                    static function ($attribute, $value, $fail): void {
                        if (mb_strlen((string)$value) > 7) {
                            $fail('The ' . $attribute . ' may not be greater than 7 symbols.');
                        }
                    },
                ]),
            ],
            'currency' => ['required', 'string'],
            'status' => ['required', 'string'],
            'bet_id' => [
                'nullable',
                'string',
                Rule::requiredIf(
                    fn() => !in_array($this->string('provider_name')->lower()->toString(), [
                        ProviderEnum::SPRIBE->value, Str::lower(ProviderEnum::IN_OUT->value)], true)
                        && !$this->isGr8Tech()
                ),
            ],
            'author_id' => ['required', 'numeric'],
            'author_email' => ['required', 'string'],
            'denomination' => [
                'nullable',
                'numeric',
                Rule::requiredIf(
                    fn() => $this->string('supplier')->lower()->toString() !== SupplierEnum::GAMICORP_INTEGRATION->value
                ),
            ],
            'start_at' => [
                'nullable',
                'date',
                Rule::requiredIf(
                    fn() => $this->string('supplier')->lower()->toString() !== SupplierEnum::GAMICORP_INTEGRATION->value
                ),
                Rule::excludeIf($this->isGr8Tech()),
            ],
            'expired_at' => [
                'required',
                'date',
                Rule::when(
                    $this->string('aggregator')->lower()->toString() === AggregatorEnum::SOFTSWISS->value,
                    ['before:' . Carbon::now()->addMonth()->toDateString()]
                ),
            ],
            'players' => [
                'array',
                Rule::requiredIf(
                    fn() => $this->string('supplier')->lower()->toString() !== SupplierEnum::GAMICORP_INTEGRATION->value,
                ),
            ],
            'players.*.uuid' => ['uuid'],
            'duration' => ['nullable', 'integer'],
            'free_spin_duration' => [
                'nullable',
                'integer',
                'digits_between:2,15',
                'min:10',
                Rule::requiredIf($this->isGr8Tech()),
            ],
            'max_transfer' => ['nullable', 'numeric'],
            'min_bet' => ['nullable', 'numeric'],
            'inout_game_name' => [
                'nullable',
                'string',
                Rule::requiredIf(fn()
                    => $this->string('provider_name')->lower()->toString() === Str::lower(ProviderEnum::IN_OUT->value)),
            ],
            'mines_count' => ['nullable', 'integer', 'min:2', 'max:24',
                Rule::requiredIf(fn()
                    => $this->string('inout_game_name')->lower()->toString() === InoutGamesEnum::LUCKY_MINES->value),
            ],
            'difficulty' => ['nullable', 'string', Rule::enum(GameDifficultyEnum::class),
                Rule::requiredIf(fn()
                => $this->string('inout_game_name')->lower()->toString() === InoutGamesEnum::CHICKEN_ROAD->value),
            ],
            'bonus_genre_id' => ['nullable', Rule::enum(SlotGenreEnum::class)],
            'bonus_slot_providers' => ['nullable', 'array'],
            'bonus_slot_providers.*' => ['numeric', 'exists:slots_providers,id'],
            'bonus_slots' => [Rule::requiredIf($this->isBonusSlotsRequired()), 'array'],
            'bonus_slots.*' => [Rule::requiredIf($this->isBonusSlotsRequired()), 'numeric', 'exists:slots,id'],
        ];
    }

    protected function isGr8Tech(): bool
    {
        return Str::lower($this->supplier) === Str::lower(SupplierEnum::GAMICORP_INTEGRATION->value)
        && AggregatorEnum::tryFrom(Str::lower($this->aggregator))?->isGr8tech();
    }

    /**
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'expired_at.before' => 'The expired_at date must be within one month from today.',
        ];
    }

    private function isBonusSlotsRequired(): bool
    {
        return $this->boolean('bonus_balance') && $this->input('bonus_slot_providers');
    }
}
