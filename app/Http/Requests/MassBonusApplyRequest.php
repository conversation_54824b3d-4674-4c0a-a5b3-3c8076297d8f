<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\BonusTypeEnum;
use App\Rules\CsvFileRule;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

/**
 * Class MassBonusApplyRequest
 *
 * @property int $bonus_id
 * @property string $bonus_type
 * @property resource $file
 */
class MassBonusApplyRequest extends BaseRequest
{
    /**
     * @return array<string, array<CsvFileRule|In|string>>
     */
    public function rules(): array
    {
        /**
         * @var null|string $bonusType
         */
        $bonusType = $this->input('bonus_type');

        $this->merge([
            'bonus_type' => $bonusType,
        ]);

        return [
            'bonus_id' => ['required', 'integer', 'min:1', 'exists:bonuses,id'],
            'bonus_type' => [
                'required',
                'string',
                Rule::in([BonusTypeEnum::ONETIME->value, BonusTypeEnum::FREEBET->value]),
            ],
            'file' => [
                'required',
                'file',
                'mimetypes:text/plain,text/csv',
                new CsvFileRule($bonusType),
            ],
        ];
    }
}
