<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\ProductTypeEnum;
use App\Traits\PaginationRulesRequestTrait;
use Illuminate\Validation\Rule;

/**
 * Class BonusSearchRequest
 *
 * @property null|string $currency
 * @property null|string $type
 * @property null|int $id
 * @property null|string $name
 * @property null|bool $bets
 * @property null|bool $casino
 * @property null|bool $active
 * @property null|string $slot_provider_id
 * @property null|string $slot_id
 * @property null|bool $is_organic
 * @property null|string $genre_id
 * @property null|string $product_type
 * @property null|int $external_id
 */
class BonusSearchRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'currency' => ['nullable', 'between:3,3'],
            'type' => ['nullable', 'string'],
            'id' => ['nullable', 'numeric'],
            'name' => ['nullable', 'string'],
            'bets' => ['nullable', 'boolean'],
            'casino' => ['nullable', 'boolean'],
            'active' => ['nullable', 'boolean'],
            'slot_provider_id' => ['nullable', 'string'],
            'slot_id' => ['nullable', 'string'],
            'is_organic' => ['nullable', 'boolean'],
            'external_id' => ['nullable', 'integer'],
            'genre_id' => ['nullable', 'string'],
            'product_type' => [
                'nullable',
                'string',
                Rule::enum(ProductTypeEnum::class),
            ],
            ...$this->paginationRules(),
        ];
    }

    /**
     * Prepare inputs for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => $this->toBoolean($this->active),
        ]);
    }

    /**
     * Convert to boolean
     *
     * @param mixed $booleable
     * @return mixed
     */
    private function toBoolean(mixed $booleable): mixed
    {
        if ($booleable === null) {
            return null;
        }

        $ret = filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);

        if ($ret === null) {
            return $booleable;
        }

        return $ret;
    }
}
