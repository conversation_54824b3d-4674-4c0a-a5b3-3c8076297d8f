<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class BetbySearchRequest
 *
 * @property null|string $type
 * @property null|bool $is_active
 * @property string $id
 */
class BetbySearchRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'type' => ['nullable', 'string'],
            'is_active' => ['nullable', 'boolean'],
            'id' => ['nullable', 'string'],
            ...$this->paginationRules(),
        ];
    }
}
