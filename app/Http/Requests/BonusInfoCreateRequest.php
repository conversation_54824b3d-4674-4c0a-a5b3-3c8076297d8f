<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class BonusInfoCreateRequest
 *
 * @property string $name
 * @property null|int $client_id
 * @property string $currency
 * @property int $visible_from
 * @property int $visible_to
 * @property bool $casino
 * @property bool $is_welcome
 * @property null|bool $active
 * @property null|string $image
 * @property null|string[] $description
 * @property null|string[] $description_info
 * @property null|string[] $condition
 * @property null|array<int, string> $condition_title
 * @property null|string[] $bonus_name
 * @property int $sort_order
 * @property null|string $proceed_link
 * @property null|int $bonus_id
 * @property null|array<int, string> $colors
 * @property null|bool $is_for_smartico
 * @property null|bool $show_visible_date
 * @property null|string $custom_type
 */
class BonusInfoCreateRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'client_id' => ['nullable', 'numeric'],
            'currency' => ['required', 'string', 'between:3,3'],
            'visible_from' => ['required', 'date_format:U'],
            'visible_to' => ['required', 'date_format:U'],
            'casino' => ['required', 'boolean'],
            'is_welcome' => ['required', 'boolean'],
            'active' => ['nullable', 'boolean'],
            'image' => ['nullable', 'string'],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string'],
            'description_info' => ['nullable', 'array'],
            'description_info.*' => ['nullable', 'string'],
            'condition' => ['nullable', 'array'],
            'condition.*' => ['nullable', 'string'],
            'condition_title' => ['nullable', 'array'],
            'bonus_name' => ['nullable', 'array'],
            'bonus_name.*' => ['nullable', 'string'],
            'sort_order' => ['required', 'numeric', 'min:1'],
            'proceed_link' => ['prohibited_if:is_welcome,true', 'nullable', 'string'],
            'bonus_id' => ['required_if:is_welcome,true,null', 'prohibited_if:proceed_link,not null', 'nullable', 'numeric', 'exists:bonuses,id'],
            'colors' => ['nullable', 'array'],
            'is_for_smartico' => ['nullable', 'boolean'],
            'show_visible_date' => ['nullable', 'boolean'],
            'custom_type' => ['nullable', 'string'],
        ];
    }
}
