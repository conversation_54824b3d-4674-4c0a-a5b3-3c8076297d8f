<?php

declare(strict_types=1);

/*
 * This file is part of the Monolog package.
 *
 * (c) <PERSON><PERSON> <j.bog<PERSON><PERSON>@seld.be>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Gelf;

use Gelf\Message;
use Monolog\Level;
use Monolog\LogRecord;
use Monolog\Utils;

/**
 * Serializes a log message to <PERSON>LF
 *
 * @see http://docs.graylog.org/en/latest/pages/gelf.html
 *
 * <AUTHOR> <<EMAIL>>
 */
class GelfMessageFormatter extends \Monolog\Formatter\GelfMessageFormatter
{
    private ?string $uniqueId;

    protected int $timeStart;

    /**
     * @throws \RuntimeException
     */
    public function __construct(?string $systemName = null, ?string $extraPrefix = null, string $contextPrefix = 'ctxt_', ?int $maxLength = null, ?string $uniqueId = null)
    {
        parent::__construct($systemName, $extraPrefix, $contextPrefix, $maxLength);

        $this->uniqueId = $uniqueId;
        $this->timeStart = (int) hrtime(true);
    }

    protected function setUniqueId(?string $uniqueId): void
    {
        $this->uniqueId = $uniqueId;
    }

    protected function setTimeStart(int $timeStart): void
    {
        $this->timeStart = $timeStart;
    }

    /**
     * Translates Monolog log levels to Graylog2 log priorities.
     */
    private function getGraylog2Priority(Level $level): int
    {
        return match ($level) {
            Level::Debug => 7,
            Level::Info => 6,
            Level::Notice => 5,
            Level::Warning => 4,
            Level::Error => 3,
            Level::Critical => 2,
            Level::Alert => 1,
            Level::Emergency => 0,
        };
    }

    /**
     * {@inheritDoc}
     */
    public function format(LogRecord $record): Message
    {
        $context = $extra = [];
        if (isset($record->context)) {
            /** @var mixed[] $context */
            $context = parent::normalize($record->context);
        }
        if (isset($record->extra)) {
            /** @var mixed[] $extra */
            $extra = parent::normalize($record->extra);
        }

        if ($context['setNewUniqueId'] ?? false) {
            $this->setUniqueId($context['setNewUniqueId']);
            $this->setTimeStart((int) hrtime(true));
        }

        $context = array_merge(
            $context,
            [
                'execute_time' => (hrtime(true) - $this->timeStart) / 1e9,
                'log_uuid' => $this->uniqueId ?? null,
            ]
        );

        $message = new Message();
        $message
            ->setTimestamp($record->datetime)
            ->setShortMessage($this->uniqueId ? "$this->uniqueId | $record->message" : $record->message)
            ->setHost($this->systemName)
            ->setLevel($this->getGraylog2Priority($record->level));

        // message length + system name length + 200 for padding / metadata
        $len = 200 + strlen($record->message) + strlen($this->systemName);

        if ($len > $this->maxLength) {
            $message->setShortMessage(Utils::substr($record->message, 0, $this->maxLength));
        }

        if (isset($record->channel)) {
            $message->setAdditional('facility', $record->channel);
        }

        foreach ($extra as $key => $val) {
            $val = is_scalar($val) || $val === null ? $val : $this->toJson($val);
            $len = strlen($this->extraPrefix.$key.$val);
            if ($len > $this->maxLength) {
                $message->setAdditional($this->extraPrefix.$key, Utils::substr((string) $val, 0, $this->maxLength));

                continue;
            }
            $message->setAdditional($this->extraPrefix.$key, $val);
        }

        foreach ($context as $key => $val) {
            $val = is_scalar($val) || $val === null ? $val : $this->toJson($val);
            $len = strlen($this->contextPrefix.$key.$val);
            if ($len > $this->maxLength) {
                $message->setAdditional($this->contextPrefix.$key, Utils::substr((string) $val, 0, $this->maxLength));

                continue;
            }
            $message->setAdditional($this->contextPrefix.$key, $val);
        }

        if (! $message->hasAdditional('file') && isset($context['exception']['file'])) {
            if (preg_match('/^(.+):([0-9]+)$/', $context['exception']['file'], $matches) === 1) {
                $message->setAdditional('file', $matches[1]);
                $message->setAdditional('line', $matches[2]);
            }
        }

        return $message;
    }
}
