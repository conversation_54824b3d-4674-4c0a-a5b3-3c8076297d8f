<?php
declare(strict_types=1);


namespace OpenAPI\Server\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use App\Http\Requests\CreateBonusRequest;
use App\Http\Requests\UpdateBonusRequest;
use App\Http\Resources\CreateBonusResource;
use App\Http\Resources\DeleteBonusResource;
use App\Http\Resources\UpdateBonusResource;
use App\Services\Dto\CreateBonusDto;
use App\Services\Dto\UpdateBonusDto;
use OpenAPI\Server\Api\BonusesApiInterface;

/**
 * The version of the OpenAPI document: 1.0.0
 *
 * Class BonusesController
 */
class BonusesController extends Controller
{
    public function __construct(
        private readonly BonusesApiInterface $service,
    ) {
    }

    /**
     * 
     */
    public function createBonus(CreateBonusRequest $request): CreateBonusResource
    {
        /** @var CreateBonusDto $dto */
        $dto = CreateBonusDto::fromArray($request->validated());

        return CreateBonusResource::make($this->service->createBonus($dto));
    }

    /**
     * 
     */
    public function deleteBonus(string $id, ): DeleteBonusResource
    {
        return DeleteBonusResource::make($this->service->deleteBonus($id, ));
    }

    /**
     * 
     */
    public function updateBonus(string $id, UpdateBonusRequest $request): UpdateBonusResource
    {
        /** @var UpdateBonusDto $dto */
        $dto = UpdateBonusDto::fromArray($request->validated());

        return UpdateBonusResource::make($this->service->updateBonus($id, $dto));
    }

}
