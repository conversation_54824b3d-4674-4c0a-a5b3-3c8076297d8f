<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


namespace OpenAPI\Server\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use App\Http\Requests\ApiBonusesAddPostRequest;
use App\Http\Resources\ApiBonusesAddPostResource;
use App\Services\Dto\ApiBonusesAddPostDto;
use OpenAPI\Server\Api\BonusesApiInterface;

/**
 * Class BonusesController
 */
class BonusesController extends Controller
{
    public function __construct(
        private readonly BonusesApiInterface $service,
    ) {
    }

    /**
     * 
     */
    public function apiBonusesAddPost(string $xSignature, string $xClusterConnection, ApiBonusesAddPostRequest $request): ApiBonusesAddPostResource
    {
        /** @var ApiBonusesAddPostDto $dto */
        $dto = ApiBonusesAddPostDto::fromArray($request->validated());

        return ApiBonusesAddPostResource::make($this->service->apiBonusesAddPost($xSignature, $xClusterConnection, $dto));
    }

}
