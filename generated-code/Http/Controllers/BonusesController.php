<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


namespace OpenAPI\Server\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;



use OpenAPI\Server\Model\ApiBonusesAddPostRequest;
use OpenAPI\Server\Model\BonusResource;
use OpenAPI\Server\Api\BonusesApiInterface;

/**
 * Class BonusesController
 */
class BonusesController extends Controller
{
    public function __construct(
        private readonly BonusesApiInterface $api,
    ) {
    }

    /**
     * 
     *
     * create bonus
     *
     * @throws Exception
     */
    public function apiBonusesAddPost(string $x, Signaturestring $xClusterConnection\OpenAPI\Server\Model\ApiBonusesAddPostRequest $apiBonusesAddPostRequest): \OpenAPI\Server\Model\BonusResource
    {
        // TODO: Implement apiBonusesAddPost method
        
        // Return single resource response
    }

}
