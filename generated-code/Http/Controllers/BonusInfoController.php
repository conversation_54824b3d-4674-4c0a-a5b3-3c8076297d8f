<?php
declare(strict_types=1);


namespace OpenAPI\Server\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use App\Http\Requests\ApiBonusesInfoIdPutRequest;
use App\Http\Resources\ApiBonusesInfoIdPutResource;
use App\Services\Dto\ApiBonusesInfoIdPutDto;
use OpenAPI\Server\Api\BonusInfoApiInterface;

/**
 * The version of the OpenAPI document: 1.0.0
 *
 * Class BonusInfoController
 */
class BonusInfoController extends Controller
{
    public function __construct(
        private readonly BonusInfoApiInterface $service,
    ) {
    }

    /**
     * 
     */
    public function apiBonusesInfoIdPut(string $id, ApiBonusesInfoIdPutRequest $request): ApiBonusesInfoIdPutResource
    {
        /** @var ApiBonusesInfoIdPutDto $dto */
        $dto = ApiBonusesInfoIdPutDto::fromArray($request->validated());

        return ApiBonusesInfoIdPutResource::make($this->service->apiBonusesInfoIdPut($id, $dto));
    }

}
