<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

/**
 * Class UpdateBonusRequest
 */
class UpdateBonusRequest extends BaseRequest
{
    /**
     * @return array<string, array<string>>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'image' => ['required', 'string'],
            'description' => ['required', 'array'],
            'condition' => ['required', 'array'],
            'bonusName' => ['required', 'array'],
            'type' => ['required', 'string'],
            'maxTransfers' => ['required', 'array'],
            'maxBonuses' => ['required', 'array'],
            'depositFactors' => ['required', 'array'],
            'data' => ['required', 'array'],
            'providersIds' => ['required', 'array'],
            'slotsIds' => ['required', 'array'],
            'triggerSessions' => ['required', 'array'],
            'minBet' => ['nullable', 'integer'],
            'minDeposit' => ['nullable', 'integer'],
            'duration' => ['nullable', 'integer'],
            'wager' => ['nullable', 'numeric'],
            'currency' => ['nullable', 'string'],
            'active' => ['nullable', 'boolean'],
            'casino' => ['nullable', 'boolean'],
            'bets' => ['nullable', 'boolean'],
            'from' => ['nullable', 'string'],
            'to' => ['nullable', 'string'],
            'minFactor' => ['nullable', 'numeric'],
            'segmentId' => ['nullable', 'integer'],
            'isPromo' => ['nullable', 'boolean'],
            'isExternal' => ['nullable', 'boolean'],
            'authorId' => ['nullable', 'integer'],
            'slotsId' => ['nullable', 'integer'],
            'providerId' => ['nullable', 'integer'],
            'bonusBalance' => ['nullable', 'boolean'],
            'count' => ['nullable', 'integer'],
            'bet' => ['nullable', 'numeric'],
            'betId' => ['nullable', 'string'],
            'denomination' => ['nullable', 'numeric'],
            'maxRealBalance' => ['nullable', 'integer'],
            'genreId' => ['nullable', 'string'],        ];
    }
}
