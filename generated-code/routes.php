<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


use Illuminate\Support\Facades\Route;

/**
 * POST apiBonusesAddPost
 * Summary: 
 * Notes: create bonus
 */
Route::POST('/api/api/bonuses/add', [\OpenAPI\Server\Http\Controllers\BonusesController::class, 'apiBonusesAddPost'])->name('bonuses.api.bonuses.add.post');

