<?php
declare(strict_types=1);


use Illuminate\Support\Facades\Route;

/**
 * POST createBonus
 * Summary: 
 * Notes: create bonus
 */
Route::POST('/api/api/bonuses/add', [\OpenAPI\Server\Http\Controllers\BonusesController::class, 'createBonus'])->name('bonuses.create.bonus');

/**
 * DELETE deleteBonus
 * Summary: 
 * Notes: delete bonus
 */
Route::DELETE('/api/api/bonuses/{id}', [\OpenAPI\Server\Http\Controllers\BonusesController::class, 'deleteBonus'])->name('bonuses.delete.bonus');

/**
 * PUT updateBonus
 * Summary: 
 * Notes: update bonus
 */
Route::PUT('/api/api/bonuses/{id}', [\OpenAPI\Server\Http\Controllers\BonusesController::class, 'updateBonus'])->name('bonuses.update.bonus');

