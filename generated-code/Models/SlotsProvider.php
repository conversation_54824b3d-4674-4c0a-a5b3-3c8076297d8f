<?php
declare(strict_types=1);


        /**
        * SlotsProvider
        */
        namespace OpenAPI\Server\Models;

        /**
        * SlotsProvider
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class SlotsProvider
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * bigint
    * @param int $subscriptionId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param null | string $customName
    *
    * string(255)
    * @param null | string $image
    *
    * boolean
    * @param bool $suspended
    *
    * boolean
    * @param bool $isBonusBarred
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Server\Models\Slot[] $getProviders
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public int $subscriptionId,
        public string $name,
        public bool $suspended = false,
        public bool $isBonusBarred = false,
        public array $getProviders,
        public ?string $customName = null,
        public ?string $image = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}


