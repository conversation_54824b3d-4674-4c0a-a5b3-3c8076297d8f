<?php
declare(strict_types=1);


        /**
        * FreeSpinHistoryLogResource
        */
        namespace OpenAPI\Server\Models;

        /**
        * FreeSpinHistoryLogResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinHistoryLogResource
{
/**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $freeSpinId
    *
    * 
    * @param string $type
    *
    * 
    * @param string $authorEmail
    *
    * 
    * @param string[] $data
    *
    * datetime
    * @param \DateTime $createdAt
*/

public function __construct(
            public int $id,
            public int $freeSpinId,
            public string $type,
            public string $authorEmail,
            public array $data,
            public \DateTime $createdAt,
) {}
}

