<?php
declare(strict_types=1);


        /**
        * TemplateFreebetDataBetRestrictions
        */
        namespace OpenAPI\Server\Models;

        /**
        * TemplateFreebetDataBetRestrictions
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class TemplateFreebetDataBetRestrictions
{
    /**
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param string[] $betsData
    */

    public function __construct(
        public string $type,
        public array $betsData,
    ) {}
}


