<?php
declare(strict_types=1);


        /**
        * UserBalance
        */
        namespace OpenAPI\Server\Models;

        /**
        * UserBalance
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UserBalance
{
/**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $playerId
    *
    * string(3)
    * @param string $currency
    *
    * integer
    * @param null | int $lastTransactionId
    *
    * bigint
    * @param int $balance
    *
    * bigint
    * @param int $debt
    *
    * bigint
    * @param int $deposit
    *
    * bigint
    * @param int $wager
    *
    * bigint
    * @param int $withdraw
    *
    * bigint
    * @param int $inGame
    *
    * bigint
    * @param int $currentPayoutLimit
    *
    * bigint
    * @param int $totalBet
    *
    * bigint
    * @param int $totalProfit
    *
    * bigint
    * @param int $payoutApproved
    *
    * bigint
    * @param int $payoutWait
*/

public function __construct(
            public int $id,
            public int $playerId,
            public string $currency,
            public int $balance,
            public int $debt,
            public int $deposit,
            public int $wager,
            public int $withdraw,
            public int $inGame,
            public int $currentPayoutLimit,
            public int $totalBet,
            public int $totalProfit,
            public int $payoutApproved,
            public int $payoutWait,
            public ?int $lastTransactionId = null,
) {}
}

