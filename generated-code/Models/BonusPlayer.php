<?php
declare(strict_types=1);


        /**
        * BonusPlayer
        */
        namespace OpenAPI\Server\Models;

        /**
        * BonusPlayer
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayer
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonusId
    *
    * bigint
    * @param int $playerId
    *
    * bigint
    * @param null | int $triggerSessionId
    *
    * string(255)
    * @param string $status
    *
    * integer
    * @param null | int $depositsCount
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public int $playerId,
        public string $status,
        public ?int $triggerSessionId = null,
        public ?int $depositsCount = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}


