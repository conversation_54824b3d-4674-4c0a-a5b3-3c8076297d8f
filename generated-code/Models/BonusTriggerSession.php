<?php
declare(strict_types=1);


        /**
        * BonusTriggerSession
        */
        namespace OpenAPI\Server\Models;

        /**
        * BonusTriggerSession
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusTriggerSession
{
/**
    *
    * datetime
    * @param \DateTime $startAt
    *
    * datetime
    * @param \DateTime $endAt
    *
    * string(36)
    * @param null | string $uuid
*/

public function __construct(
            public \DateTime $startAt,
            public \DateTime $endAt,
            public ?string $uuid = null,
) {}
}

