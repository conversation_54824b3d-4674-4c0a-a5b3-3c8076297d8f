<?php
declare(strict_types=1);


        /**
        * BonusBalance
        */
        namespace OpenAPI\Server\Models;

        /**
        * BonusBalance
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusBalance
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param null | string $logKey
    *
    * bigint
    * @param int $bonusId
    *
    * string(40)
    * @param string $type
    *
    * bigint
    * @param int $playerId
    *
    * bigint
    * @param int $balance
    *
    * string(255)
    * @param null | string $bonusExternalId
    *
    * string(40)
    * @param string $status
    *
    * bigint
    * @param int $origBonus
    *
    * bigint
    * @param int $origWager
    *
    * bigint
    * @param int $wager
    *
    * float
    * @param null | float $minFactor
    *
    * bigint
    * @param null | int $minBet
    *
    * datetime
    * @param null | \DateTime $expireAt
    *
    * bigint
    * @param int $inGame
    *
    * bigint
    * @param int $transfer
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $popupSeen
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Models\User $player
    *
    * 
    * @param \OpenAPI\Server\Models\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public string $type = 'wager',
        public int $playerId,
        public int $balance,
        public string $status,
        public int $origBonus,
        public int $origWager,
        public int $wager,
        public int $inGame,
        public int $transfer,
        public string $currency,
        public bool $active,
        public bool $popupSeen = false,
        public bool $casino,
        public bool $bets,
        public \OpenAPI\Server\Models\User $player,
        public \OpenAPI\Server\Models\Bonus $bonus,
        public ?string $logKey = null,
        public ?string $bonusExternalId = null,
        public ?float $minFactor = null,
        public ?int $minBet = null,
        public ?\DateTime $expireAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}


