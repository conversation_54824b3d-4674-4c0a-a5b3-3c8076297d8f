<?php
declare(strict_types=1);


        /**
        * BonusHistoryLogResource
        */
        namespace OpenAPI\Server\Models;

        /**
        * BonusHistoryLogResource
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusHistoryLogResource
{
/**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $bonusId
    *
    * 
    * @param string $type
    *
    * 
    * @param string $authorEmail
    *
    * 
    * @param string[] $data
    *
    * datetime
    * @param \DateTime $createdAt
*/

public function __construct(
            public int $id,
            public int $bonusId,
            public string $type,
            public string $authorEmail,
            public array $data,
            public \DateTime $createdAt,
) {}
}

