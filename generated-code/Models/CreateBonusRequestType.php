<?php
declare(strict_types=1);


        /**
        * CreateBonusRequestType
        */
        namespace OpenAPI\Server\Models;

        /**
        * CreateBonusRequestType
        */
            enum CreateBonusRequestType: string
{
        case WELCOME = 'welcome';
        case WELCOME_4_STEPS = 'welcome_4_steps';
        case ONETIME = 'onetime';
        case NO_DEP = 'no_dep';
        case DEPOSIT = 'deposit';
        case FREESPIN_BONUS = 'freespin_bonus';
        case FREE_SPINS_FOR_DEPOSIT = 'free_spins_for_deposit';
        case WAGER = 'wager';
        case FREEBET = 'freebet';
        case FREE_MONEY = 'free_money';
        case BET_REFUND = 'bet_refund';
        case NO_RISK = 'no_risk';
}
