<?php
declare(strict_types=1);


        /**
        * UpdateBonusRequest
        */
        namespace OpenAPI\Server\Models;

        /**
        * UpdateBonusRequest
        */
            use <PERSON><PERSON>\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UpdateBonusRequest
{
/**
    *
    * 
    * @param string $name
    *
    * 
    * @param string $image
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param \OpenAPI\Server\Models\CreateBonusRequestType $type
    *
    * 
    * @param null | int $minBet
    *
    * 
    * @param null | int $minDeposit
    *
    * 
    * @param int[] $maxTransfers
    *
    * 
    * @param int[] $maxBonuses
    *
    * 
    * @param int[] $depositFactors
    *
    * 
    * @param null | int $duration
    *
    * 
    * @param null | float $wager
    *
    * 
    * @param null | string $currency
    *
    * 
    * @param null | bool $active
    *
    * 
    * @param null | bool $casino
    *
    * 
    * @param null | bool $bets
    *
    * 
    * @param null | string $from
    *
    * 
    * @param null | string $to
    *
    * 
    * @param null | float $minFactor
    *
    * 
    * @param object $data
    *
    * 
    * @param int[] $providersIds
    *
    * 
    * @param int[] $slotsIds
    *
    * required if type is free_spins_for_deposit
    * @param \OpenAPI\Server\Models\BonusTriggerSession[] $triggerSessions
    *
    * 
    * @param null | int $segmentId
    *
    * 
    * @param null | bool $isPromo
    *
    * 
    * @param null | bool $isExternal
    *
    * required if type is free_spins_for_deposit
    * @param null | int $authorId
    *
    * required if type is free_spins_for_deposit
    * @param null | int $slotsId
    *
    * required if type is free_spins_for_deposit
    * @param null | int $providerId
    *
    * required if type is free_spins_for_deposit
    * @param null | bool $bonusBalance
    *
    * required if type is free_spins_for_deposit
    * @param null | int $count
    *
    * required if type is free_spins_for_deposit
    * @param null | float $bet
    *
    * required if type is free_spins_for_deposit
    * @param null | string $betId
    *
    * required if type is free_spins_for_deposit
    * @param null | float $denomination
    *
    * 
    * @param null | int $maxRealBalance
    *
    * 
    * @param null | \OpenAPI\Server\Models\CreateBonusRequestGenreId $genreId
*/

public function __construct(
            public string $name,
            public string $image,
            public object $description,
            public object $condition,
            public object $bonusName,
            public \OpenAPI\Server\Models\CreateBonusRequestType $type,
            public array $maxTransfers,
            public array $maxBonuses,
            public array $depositFactors,
            public object $data,
            public array $providersIds,
            public array $slotsIds,
            public array $triggerSessions,
            public ?int $minBet = null,
            public ?int $minDeposit = null,
            public ?int $duration = null,
            public ?float $wager = null,
            public ?string $currency = null,
            public ?bool $active = null,
            public ?bool $casino = null,
            public ?bool $bets = null,
            public ?string $from = null,
            public ?string $to = null,
            public ?float $minFactor = null,
            public ?int $segmentId = null,
            public ?bool $isPromo = null,
            public ?bool $isExternal = null,
            public ?int $authorId = null,
            public ?int $slotsId = null,
            public ?int $providerId = null,
            public ?bool $bonusBalance = null,
            public ?int $count = null,
            public ?float $bet = null,
            public ?string $betId = null,
            public ?float $denomination = null,
            public ?int $maxRealBalance = null,
            public ?\OpenAPI\Server\Models\CreateBonusRequestGenreId $genreId = null,
) {}
}

