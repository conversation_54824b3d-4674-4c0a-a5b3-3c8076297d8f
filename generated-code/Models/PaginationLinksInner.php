<?php
declare(strict_types=1);


        /**
        * PaginationLinksInner
        */
        namespace OpenAPI\Server\Models;

        /**
        * PaginationLinksInner
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationLinksInner
{
    /**
    *
    * URL
    * @param string $url
    *
    * Label
    * @param float $label
    *
    * Active
    * @param bool $active
    */

    public function __construct(
        public string $url,
        public float $label,
        public bool $active,
    ) {}
}


