<?php
declare(strict_types=1);


        /**
        * FreeSpinBoundResource
        */
        namespace OpenAPI\Server\Models;

        /**
        * FreeSpinBoundResource
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBoundResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $freeSpinId
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonusId
    *
    * bigint
    * @param int $bet
    *
    * integer
    * @param int $totalWin
    *
    * integer
    * @param int $countLeft
    *
    * boolean
    * @param bool $isActive
    *
    * boolean
    * @param bool $isArchived
    *
    * datetime
    * @param null | float $startAt
    *
    * datetime
    * @param null | float $expireAt
    *
    * datetime
    * @param null | float $canceledAt
    *
    * datetime
    * @param null | float $createdAt
    *
    * datetime
    * @param null | float $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Models\FreeSpinResource $freespin
    *
    * 
    * @param int $slotId
    *
    * 
    * @param \OpenAPI\Server\Models\BonusResource $bonus
    */

    public function __construct(
        public int $id,
        public int $freeSpinId,
        public string $uuid,
        public int $bet,
        public int $totalWin,
        public int $countLeft,
        public bool $isActive = true,
        public bool $isArchived = false,
        public \OpenAPI\Server\Models\FreeSpinResource $freespin,
        public int $slotId,
        public \OpenAPI\Server\Models\BonusResource $bonus,
        public ?int $bonusId = null,
        public ?float $startAt = null,
        public ?float $expireAt = null,
        public ?float $canceledAt = null,
        public ?float $createdAt = null,
        public ?float $updatedAt = null,
    ) {}
}


