<?php
declare(strict_types=1);


        /**
        * NoContent401
        */
        namespace OpenAPI\Server\Models;

        /**
        * NoContent401
            * @description No content for 401
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class NoContent401
{
/**
    *
    * dummy property for no-content responses
    * @param null | string $dummy
*/

public function __construct(
            public ?string $dummy = null,
) {}
}

