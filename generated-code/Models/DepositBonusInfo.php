<?php
declare(strict_types=1);


        /**
        * DepositBonusInfo
        */
        namespace OpenAPI\Server\Models;

        /**
        * DepositBonusInfo
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class DepositBonusInfo
{
/**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonusId
    *
    * bigint
    * @param int $smarticoBonusId
    *
    * bigint
    * @param null | int $playerId
    *
    * string(255)
    * @param null | string $email
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
*/

public function __construct(
            public int $id,
            public int $bonusId,
            public int $smarticoBonusId,
            public string $status = 'in_process',
            public ?int $playerId = null,
            public ?string $email = null,
            public ?int $amount = null,
            public ?string $text = null,
            public ?\DateTime $createdAt = null,
            public ?\DateTime $updatedAt = null,
) {}
}

