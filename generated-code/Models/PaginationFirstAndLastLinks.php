<?php
declare(strict_types=1);


        /**
        * PaginationFirstAndLastLinks
        */
        namespace OpenAPI\Server\Models;

        /**
        * PaginationFirstAndLastLinks
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationFirstAndLastLinks
{
/**
    *
    * First
    * @param string $first
    *
    * Last
    * @param string $last
    *
    * Previous
    * @param null | string $prev
    *
    * Next
    * @param string $next
*/

public function __construct(
            public string $first,
            public string $last,
            public string $next,
            public ?string $prev = null,
) {}
}

