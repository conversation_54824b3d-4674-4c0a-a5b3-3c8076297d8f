<?php
declare(strict_types=1);


        /**
        * BonusInfo
        */
        namespace OpenAPI\Server\Models;

        /**
        * BonusInfo
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * integer
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * datetime
    * @param null | \DateTime $visibleFrom
    *
    * datetime
    * @param null | \DateTime $visibleTo
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param null | bool $isWelcome
    *
    * boolean
    * @param bool $isForSmartico
    *
    * string(255)
    * @param null | string $image
    *
    * json
    * @param null | string $bonusName
    *
    * json
    * @param null | string $description
    *
    * json
    * @param null | string $descriptionInfo
    *
    * json
    * @param null | string $conditionTitle
    *
    * json
    * @param null | string $condition
    *
    * json
    * @param null | string $colors
    *
    * integer
    * @param int $sortOrder
    *
    * string(255)
    * @param null | string $proceedLink
    *
    * bigint
    * @param null | int $bonusId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Server\Models\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $currency,
        public bool $active,
        public bool $casino,
        public bool $isForSmartico = false,
        public int $sortOrder,
        public \OpenAPI\Server\Models\Bonus $bonus,
        public ?\DateTime $visibleFrom = null,
        public ?\DateTime $visibleTo = null,
        public ?bool $isWelcome = false,
        public ?string $image = null,
        public ?string $bonusName = null,
        public ?string $description = null,
        public ?string $descriptionInfo = null,
        public ?string $conditionTitle = null,
        public ?string $condition = null,
        public ?string $colors = null,
        public ?string $proceedLink = null,
        public ?int $bonusId = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}


