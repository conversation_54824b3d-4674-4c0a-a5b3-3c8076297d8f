<?php
declare(strict_types=1);


        /**
        * TemplateFreebetData
        */
        namespace OpenAPI\Server\Models;

        /**
        * TemplateFreebetData
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class TemplateFreebetData
{
    /**
    *
    * bigint
    * @param int $version
    *
    * boolean
    * @param bool $isApiAmount
    *
    * 
    * @param int[] $amountList
    *
    * string(255)
    * @param string $type
    *
    * integer
    * @param int $minSelection
    *
    * integer
    * @param int $maxSelection
    *
    * 
    * @param int $minOdd
    *
    * 
    * @param int $maxOdd
    *
    * 
    * @param bool $boundRefund
    *
    * 
    * @param object $description
    *
    * string(255)
    * @param null | string $reserveTemplate
    *
    * 
    * @param \OpenAPI\Server\Models\TemplateFreebetDataBetRestrictions $betRestrictions
    */

    public function __construct(
        public int $version,
        public bool $isApiAmount = false,
        public array $amountList,
        public string $type,
        public int $minSelection,
        public int $maxSelection,
        public int $minOdd,
        public int $maxOdd,
        public bool $boundRefund,
        public object $description,
        public \OpenAPI\Server\Models\TemplateFreebetDataBetRestrictions $betRestrictions,
        public ?string $reserveTemplate = null,
    ) {}
}


