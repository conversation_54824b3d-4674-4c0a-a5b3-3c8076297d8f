<?php
declare(strict_types=1);


        /**
        * DataSubscriptions
        */
        namespace OpenAPI\Server\Models;

        /**
        * DataSubscriptions
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class DataSubscriptions
{
/**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $subId
    *
    * string(255)
    * @param string $accessToken
    *
    * string(255)
    * @param null | string $gameToken
    *
    * string(255)
    * @param string $provider
    *
    * string(255)
    * @param string $providerCompany
    *
    * boolean
    * @param bool $isApproved
    *
    * boolean
    * @param bool $isSlot
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
*/

public function __construct(
            public int $id,
            public int $clientId,
            public string $subId,
            public string $accessToken,
            public string $provider,
            public string $providerCompany,
            public bool $isApproved = false,
            public bool $isSlot = false,
            public ?string $gameToken = null,
            public ?\DateTime $createdAt = null,
            public ?\DateTime $updatedAt = null,
            public ?string $uuid = null,
) {}
}

