<?php
declare(strict_types=1);


        /**
        * BonusForPlayerResourceMaxBonus
        */
        namespace OpenAPI\Server\Models;

        /**
        * BonusForPlayerResourceMaxBonus
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusForPlayerResourceMaxBonus
{
/**
    *
    * 
    * @param string $amount
    *
    * string(3)
    * @param string $currency
*/

public function __construct(
            public string $amount,
            public string $currency,
) {}
}

