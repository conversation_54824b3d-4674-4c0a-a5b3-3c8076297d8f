<?php
declare(strict_types=1);


namespace OpenAPI\Server\Api;


interface BonusInfoApiInterface {


    /**
     * Operation apiBonusesInfoIdPut
     *
     * 
     * @param string $id
     * @param null | string $xSignature
     * @param null | string $xClusterConnection
     * @param null | \OpenAPI\Server\Model\ApiBonusesInfoIdPutRequest $apiBonusesInfoIdPutRequest
     * @return \OpenAPI\Server\Model\BonusInfoResource | \OpenAPI\Server\Model\NoContent422
     */
    public function apiBonusesInfoIdPut(
            string $id,
            ?string $xSignature,
            ?string $xClusterConnection,
            ?\OpenAPI\Server\Model\ApiBonusesInfoIdPutRequest $apiBonusesInfoIdPutRequest,
    ):
        \OpenAPI\Server\Model\BonusInfoResource | 
        \OpenAPI\Server\Model\NoContent422
    ;

}
