<?php
declare(strict_types=1);


namespace OpenAPI\Server\Api;


interface BonusesApiInterface {


    /**
     * Operation createBonus
     *
     * 
     * @param null | string $xSignature
     * @param null | string $xClusterConnection
     * @param null | \OpenAPI\Server\Models\CreateBonusRequest $createBonusRequest
     * @return \OpenAPI\Server\Models\BonusResource | \OpenAPI\Server\Models\NoContent401
     */
    public function createBonus(
            ?string $xSignature,
            ?string $xClusterConnection,
            ?\OpenAPI\Server\Models\CreateBonusRequest $createBonusRequest,
    ):
        \OpenAPI\Server\Models\BonusResource | 
        \OpenAPI\Server\Models\NoContent401
    ;


    /**
     * Operation deleteBonus
     *
     * 
     * @param string $id
     * @param null | string $xSignature
     * @param null | string $xClusterConnection
     * @return \OpenAPI\Server\Models\NoContent200 | \OpenAPI\Server\Models\NoContent401
     */
    public function deleteBonus(
            string $id,
            ?string $xSignature,
            ?string $xClusterConnection,
    ):
        \OpenAPI\Server\Models\NoContent200 | 
        \OpenAPI\Server\Models\NoContent401
    ;


    /**
     * Operation updateBonus
     *
     * 
     * @param string $id
     * @param null | string $xSignature
     * @param null | string $xClusterConnection
     * @param null | \OpenAPI\Server\Models\UpdateBonusRequest $updateBonusRequest
     * @return \OpenAPI\Server\Models\BonusResource | \OpenAPI\Server\Models\NoContent401
     */
    public function updateBonus(
            string $id,
            ?string $xSignature,
            ?string $xClusterConnection,
            ?\OpenAPI\Server\Models\UpdateBonusRequest $updateBonusRequest,
    ):
        \OpenAPI\Server\Models\BonusResource | 
        \OpenAPI\Server\Models\NoContent401
    ;

}
