<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


namespace OpenAPI\Server\Api;


interface BonusesApiInterface {


    /**
     * Operation apiBonusesAddPost
     *
     * 
     * @param null | string $xSignature
     * @param null | string $xClusterConnection
     * @param null | \OpenAPI\Server\Model\ApiBonusesAddPostRequest $apiBonusesAddPostRequest
     * @return \OpenAPI\Server\Model\BonusResource | \OpenAPI\Server\Model\NoContent401
     */
    public function apiBonusesAddPost(
            ?string $xSignature,
            ?string $xClusterConnection,
            ?\OpenAPI\Server\Model\ApiBonusesAddPostRequest $apiBonusesAddPostRequest,
    ):
        \OpenAPI\Server\Model\BonusResource | 
        \OpenAPI\Server\Model\NoContent401
    ;

}
