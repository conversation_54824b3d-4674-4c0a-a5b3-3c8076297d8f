.gitignore
Api/BonusesApiInterface.php
Http/Controllers/BonusesController.php
Models/ArrayOfUserObjectsInner.php
Models/BannerResource.php
Models/BannerResourceType.php
Models/Bonus.php
Models/BonusBalance.php
Models/BonusForPlayerResource.php
Models/BonusForPlayerResourceMaxBonus.php
Models/BonusHistoryLogResource.php
Models/BonusInfo.php
Models/BonusInfoResource.php
Models/BonusInfoResourceBonus.php
Models/BonusPlayer.php
Models/BonusPlayerCard.php
Models/BonusResource.php
Models/BonusTriggerSession.php
Models/CreateBonusRequest.php
Models/CreateBonusRequestGenreId.php
Models/CreateBonusRequestType.php
Models/DataSubscriptions.php
Models/DepositBonusInfo.php
Models/FreeSpin.php
Models/FreeSpinBound.php
Models/FreeSpinBoundDataResource.php
Models/FreeSpinBoundResource.php
Models/FreeSpinHistoryLogResource.php
Models/FreeSpinResource.php
Models/MassFreeBetBonusInfo.php
Models/MassOnetimeBonusInfo.php
Models/NoContent200.php
Models/NoContent401.php
Models/PaginationFirstAndLastLinks.php
Models/PaginationLinksInner.php
Models/PaginationMeta.php
Models/Player.php
Models/PromoCode.php
Models/PromoCodeResource.php
Models/Slot.php
Models/SlotsProvider.php
Models/Template.php
Models/TemplateFreebetData.php
Models/TemplateFreebetDataBetRestrictions.php
Models/UpdateBonusRequest.php
Models/User.php
Models/UserBalance.php
README.md
composer.json
phpunit.xml.dist
routes.php
