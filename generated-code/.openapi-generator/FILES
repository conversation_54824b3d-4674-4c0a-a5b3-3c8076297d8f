.gitignore
Api/BonusesApiInterface.php
Http/Controllers/BonusesController.php
Model/ArrayOfUserObjectsInner.php
Model/BannerResource.php
Model/BannerResourceType.php
Model/Bonus.php
Model/BonusBalance.php
Model/BonusForPlayerResource.php
Model/BonusForPlayerResourceMaxBonus.php
Model/BonusHistoryLogResource.php
Model/BonusInfo.php
Model/BonusInfoResource.php
Model/BonusInfoResourceBonus.php
Model/BonusPlayer.php
Model/BonusPlayerCard.php
Model/BonusResource.php
Model/BonusTriggerSession.php
Model/CreateBonusRequest.php
Model/CreateBonusRequestGenreId.php
Model/CreateBonusRequestType.php
Model/DataSubscriptions.php
Model/DepositBonusInfo.php
Model/FreeSpin.php
Model/FreeSpinBound.php
Model/FreeSpinBoundDataResource.php
Model/FreeSpinBoundResource.php
Model/FreeSpinHistoryLogResource.php
Model/FreeSpinResource.php
Model/MassFreeBetBonusInfo.php
Model/MassOnetimeBonusInfo.php
Model/NoContent200.php
Model/NoContent401.php
Model/PaginationFirstAndLastLinks.php
Model/PaginationLinksInner.php
Model/PaginationMeta.php
Model/Player.php
Model/PromoCode.php
Model/PromoCodeResource.php
Model/Slot.php
Model/SlotsProvider.php
Model/Template.php
Model/TemplateFreebetData.php
Model/TemplateFreebetDataBetRestrictions.php
Model/UpdateBonusRequest.php
Model/User.php
Model/UserBalance.php
README.md
composer.json
phpunit.xml.dist
routes.php
