<?php
declare(strict_types=1);


/**
 * Template
 */
namespace OpenAPI\Server\Model;

/**
 * Template
 */
use <PERSON><PERSON>\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Template
{
    /**
    *
    * bigint
    * @param int $version
    *
    * string(255)
    * @param string $id
    *
    * text
    * @param null | string $name
    *
    * boolean
    * @param bool $isActive
    *
    * integer
    * @param int $maxBonusNumber
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $operatorId
    *
    * string(255)
    * @param string $brandId
    *
    * datetime
    * @param null | string $eventScheduled
    *
    * datetime
    * @param null | float $fromTime
    *
    * datetime
    * @param null | float $toTime
    *
    * 
    * @param float $daysToUse
    *
    * text
    * @param null | string $eventsAvailability
    *
    * text
    * @param null | string $restrictions
    *
    * 
    * @param \OpenAPI\Server\Model\TemplateFreebetData $freebetData
    */

    public function __construct(
        public int $version,
        public string $id,
        public bool $isActive = false,
        public int $maxBonusNumber,
        public string $type,
        public string $operatorId,
        public string $brandId,
        public float $daysToUse,
        public \OpenAPI\Server\Model\TemplateFreebetData $freebetData,
        public ?string $name = null,
        public ?string $eventScheduled = null,
        public ?float $fromTime = null,
        public ?float $toTime = null,
        public ?string $eventsAvailability = null,
        public ?string $restrictions = null,
    ) {}
}

