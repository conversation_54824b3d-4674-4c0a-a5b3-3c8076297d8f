<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * Template
 */
namespace OpenAPI\Server\Model;

/**
 * Template
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Template
{
    /**
    *
    * bigint
    * @param int $version
    *
    * string(255)
    * @param string $id
    *
    * text
    * @param null | string $name
    *
    * boolean
    * @param bool $isActive
    *
    * integer
    * @param int $maxBonusNumber
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $operatorId
    *
    * string(255)
    * @param string $brandId
    *
    * datetime
    * @param null | string $eventScheduled
    *
    * datetime
    * @param null | float $fromTime
    *
    * datetime
    * @param null | float $toTime
    *
    * 
    * @param float $daysToUse
    *
    * text
    * @param null | string $eventsAvailability
    *
    * text
    * @param null | string $restrictions
    *
    * 
    * @param \OpenAPI\Server\Model\TemplateFreebetData $freebetData
    */

    public function __construct(
        public int $version,
        public string $id,
        public bool $isActive = false,
        public int $maxBonusNumber,
        public string $type,
        public string $operatorId,
        public string $brandId,
        public float $daysToUse,
        public \OpenAPI\Server\Model\TemplateFreebetData $freebetData,
        public ?string $name = null,
        public ?string $eventScheduled = null,
        public ?float $fromTime = null,
        public ?float $toTime = null,
        public ?string $eventsAvailability = null,
        public ?string $restrictions = null,
    ) {}
}

