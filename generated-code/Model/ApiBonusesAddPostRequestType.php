<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * ApiBonusesAddPostRequestType
 */
namespace OpenAPI\Server\Model;

/**
 * ApiBonusesAddPostRequestType
 */
enum ApiBonusesAddPostRequestType: string
{
        case WELCOME = 'welcome';
        case WELCOME_4_STEPS = 'welcome_4_steps';
        case ONETIME = 'onetime';
        case NO_DEP = 'no_dep';
        case DEPOSIT = 'deposit';
        case FREESPIN_BONUS = 'freespin_bonus';
        case FREE_SPINS_FOR_DEPOSIT = 'free_spins_for_deposit';
        case WAGER = 'wager';
        case FREEBET = 'freebet';
        case FREE_MONEY = 'free_money';
        case BET_REFUND = 'bet_refund';
        case NO_RISK = 'no_risk';
}
