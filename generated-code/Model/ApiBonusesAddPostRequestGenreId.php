<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * ApiBonusesAddPostRequestGenreId
 */
namespace OpenAPI\Server\Model;

/**
 * ApiBonusesAddPostRequestGenreId
 */
enum ApiBonusesAddPostRequestGenreId: int
{
        case NUMBER_0 = 0;
        case NUMBER_1 = 1;
        case NUMBER_2 = 2;
        case NUMBER_3 = 3;
        case NUMBER_4 = 4;
        case NUMBER_5 = 5;
        case NUMBER_6 = 6;
        case NUMBER_7 = 7;
        case NUMBER_8 = 8;
}
