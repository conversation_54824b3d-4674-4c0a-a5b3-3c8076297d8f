<?php
declare(strict_types=1);


/**
 * BannerResource
 */
namespace OpenAPI\Server\Model;

/**
 * BannerResource
 */
use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BannerResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(2)
    * @param null | string $language
    *
    * string(3)
    * @param null | string $country
    *
    * string(255)
    * @param string $isForSignedIn
    *
    * string(255)
    * @param string $platform
    *
    * boolean
    * @param bool $mobileApkInstall
    *
    * string(255)
    * @param string $location
    *
    * bigint
    * @param int $weight
    *
    * 
    * @param \OpenAPI\Server\Model\BannerResourceType $type
    *
    * 
    * @param object $disposition
    *
    * string(255)
    * @param string $image
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $isActive
    *
    * integer
    * @param null | int $smarticoSegmentId
    *
    * integer
    * @param null | int $startAt
    *
    * integer
    * @param null | int $endAt
    *
    * string(255)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $isForSignedIn,
        public string $platform,
        public bool $mobileApkInstall,
        public string $location,
        public int $weight = 0,
        public \OpenAPI\Server\Model\BannerResourceType $type,
        public object $disposition,
        public string $image,
        public bool $enabled,
        public bool $isActive,
        public ?string $language = null,
        public ?string $country = null,
        public ?int $smarticoSegmentId = null,
        public ?int $startAt = null,
        public ?int $endAt = null,
        public ?string $uuid = null,
    ) {}
}

