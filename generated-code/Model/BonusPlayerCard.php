<?php
declare(strict_types=1);


/**
 * BonusPlayerCard
 */
namespace OpenAPI\Server\Model;

/**
 * BonusPlayerCard
 */
use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayerCard
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $playerId
    *
    * boolean
    * @param bool $isUsed
    *
    * integer
    * @param null | int $bonusId
    *
    * integer
    * @param null | int $bonusInfoId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\User $player
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $playerId,
        public bool $isUsed = false,
        public \OpenAPI\Server\Model\User $player,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public ?int $bonusId = null,
        public ?int $bonusInfoId = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

