<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * BonusPlayerCard
 */
namespace OpenAPI\Server\Model;

/**
 * BonusPlayerCard
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayerCard
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $playerId
    *
    * boolean
    * @param bool $isUsed
    *
    * integer
    * @param null | int $bonusId
    *
    * integer
    * @param null | int $bonusInfoId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\User $player
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $playerId,
        public bool $isUsed = false,
        public \OpenAPI\Server\Model\User $player,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public ?int $bonusId = null,
        public ?int $bonusInfoId = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

