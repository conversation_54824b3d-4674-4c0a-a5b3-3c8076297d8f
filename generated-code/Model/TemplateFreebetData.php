<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * TemplateFreebetData
 */
namespace OpenAPI\Server\Model;

/**
 * TemplateFreebetData
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class TemplateFreebetData
{
    /**
    *
    * bigint
    * @param int $version
    *
    * boolean
    * @param bool $isApiAmount
    *
    * 
    * @param int[] $amountList
    *
    * string(255)
    * @param string $type
    *
    * integer
    * @param int $minSelection
    *
    * integer
    * @param int $maxSelection
    *
    * 
    * @param int $minOdd
    *
    * 
    * @param int $maxOdd
    *
    * 
    * @param bool $boundRefund
    *
    * 
    * @param object $description
    *
    * string(255)
    * @param null | string $reserveTemplate
    *
    * 
    * @param \OpenAPI\Server\Model\TemplateFreebetDataBetRestrictions $betRestrictions
    */

    public function __construct(
        public int $version,
        public bool $isApiAmount = false,
        public array $amountList,
        public string $type,
        public int $minSelection,
        public int $maxSelection,
        public int $minOdd,
        public int $maxOdd,
        public bool $boundRefund,
        public object $description,
        public \OpenAPI\Server\Model\TemplateFreebetDataBetRestrictions $betRestrictions,
        public ?string $reserveTemplate = null,
    ) {}
}

