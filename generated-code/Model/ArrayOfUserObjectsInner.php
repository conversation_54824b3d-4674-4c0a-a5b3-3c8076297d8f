<?php
declare(strict_types=1);


/**
 * ArrayOfUserObjectsInner
 */
namespace OpenAPI\Server\Model;

/**
 * ArrayOfUserObjectsInner
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class ArrayOfUserObjectsInner
{
    /**
    *
    * bigint
    * @param int $id
    */

    public function __construct(
        public int $id,
    ) {}
}

