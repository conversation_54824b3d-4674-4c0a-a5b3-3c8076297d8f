<?php
declare(strict_types=1);


/**
 * BonusForPlayerResource
 */
namespace OpenAPI\Server\Model;

/**
 * BonusForPlayerResource
 */
use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusForPlayerResource
{
    /**
    *
    * 
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param string $type
    *
    * 
    * @param null | string $image
    *
    * boolean
    * @param bool $crash
    *
    * 
    * @param \OpenAPI\Server\Model\BonusForPlayerResourceMaxBonus $maxBonus
    *
    * 
    * @param int[] $maxBonuses
    *
    * 
    * @param int[] $maxTransfers
    *
    * 
    * @param int[] $depositFactors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $minDeposit
    *
    * bigint
    * @param null | int $minBet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * datetime
    * @param null | \DateTime $from
    *
    * datetime
    * @param null | \DateTime $to
    *
    * bigint
    * @param null | int $duration
    *
    * 
    * @param object $bonusData
    *
    * boolean
    * @param bool $isWelcome
    *
    * boolean
    * @param bool $isOnetime
    *
    * boolean
    * @param bool $isNoDep
    *
    * boolean
    * @param bool $isDeposit
    *
    * boolean
    * @param bool $isFreeSpinForDeposit
    *
    * 
    * @param int $weight
    *
    * boolean
    * @param bool $isOrganic
    *
    * 
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $triggerSessions
    */

    public function __construct(
        public int $id,
        public string $name,
        public object $bonusName,
        public object $description,
        public object $condition,
        public string $type = 'wager',
        public bool $crash = false,
        public \OpenAPI\Server\Model\BonusForPlayerResourceMaxBonus $maxBonus,
        public array $maxBonuses,
        public array $maxTransfers,
        public array $depositFactors,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public object $bonusData,
        public bool $isWelcome,
        public bool $isOnetime,
        public bool $isNoDep,
        public bool $isDeposit,
        public bool $isFreeSpinForDeposit,
        public int $weight,
        public bool $isOrganic,
        public array $triggerSessions,
        public ?string $image = null,
        public ?int $minDeposit = null,
        public ?int $minBet = null,
        public ?\DateTime $from = null,
        public ?\DateTime $to = null,
        public ?int $duration = null,
    ) {}
}

