<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * MassFreeBetBonusInfo
 */
namespace OpenAPI\Server\Model;

/**
 * MassFreeBetBonusInfo
 */
use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class MassFreeBetBonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonusId
    *
    * string(255)
    * @param null | string $smarticoBonusId
    *
    * bigint
    * @param null | int $templateId
    *
    * string(255)
    * @param null | string $externalPlayerId
    *
    * string(255)
    * @param string $currency
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * bigint
    * @param null | int $playerId
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public string $currency,
        public string $status = 'in_process',
        public ?string $smarticoBonusId = null,
        public ?int $templateId = null,
        public ?string $externalPlayerId = null,
        public ?int $amount = null,
        public ?string $text = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?int $playerId = null,
    ) {}
}

