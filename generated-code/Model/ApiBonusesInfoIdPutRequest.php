<?php
declare(strict_types=1);


/**
 * ApiBonusesInfoIdPutRequest
 */
namespace OpenAPI\Server\Model;

/**
 * ApiBonusesInfoIdPutRequest
 */
use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class ApiBonusesInfoIdPutRequest
{
    /**
    *
    * 
    * @param string $name
    *
    * 
    * @param float $clientId
    *
    * 
    * @param string $currency
    *
    * 
    * @param bool $active
    *
    * 
    * @param float $visibleFrom
    *
    * 
    * @param float $visibleTo
    *
    * 
    * @param bool $casino
    *
    * 
    * @param bool $isWelcome
    *
    * 
    * @param string $image
    *
    * 
    * @param object $description
    *
    * 
    * @param object $descriptionInfo
    *
    * 
    * @param object $condition
    *
    * 
    * @param object $conditionTitle
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param float $sortOrder
    *
    * 
    * @param string $proceedLink
    *
    * 
    * @param float $bonusId
    *
    * 
    * @param string[] $colors
    *
    * 
    * @param bool $isForSmartico
    */

    public function __construct(
        public string $name,
        public float $clientId = 2,
        public string $currency,
        public bool $active = true,
        public float $visibleFrom,
        public float $visibleTo,
        public bool $casino,
        public bool $isWelcome,
        public string $image,
        public object $description,
        public object $descriptionInfo,
        public object $condition,
        public object $conditionTitle,
        public object $bonusName,
        public float $sortOrder,
        public string $proceedLink,
        public float $bonusId,
        public array $colors,
        public bool $isForSmartico,
    ) {}
}

