<?php
declare(strict_types=1);


/**
 * Bonus
 */
namespace OpenAPI\Server\Model;

/**
 * Bonus
 */
use <PERSON><PERSON>\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Bonus
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * json
    * @param null | string $bonusName
    *
    * text
    * @param null | string $description
    *
    * json
    * @param null | string $condition
    *
    * string(40)
    * @param string $type
    *
    * string(255)
    * @param null | string $image
    *
    * json
    * @param string $bonuses
    *
    * json
    * @param string $maxTransfers
    *
    * json
    * @param null | string $depositFactors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $minDeposit
    *
    * float
    * @param null | float $minFactor
    *
    * bigint
    * @param null | int $minBet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * boolean
    * @param bool $crash
    *
    * bigint
    * @param int $totalTransferred
    *
    * integer
    * @param int $uses
    *
    * bigint
    * @param int $transfers
    *
    * bigint
    * @param int $totalUses
    *
    * boolean
    * @param bool $isExternal
    *
    * boolean
    * @param bool $isPromo
    *
    * datetime
    * @param null | \DateTime $activeFrom
    *
    * datetime
    * @param null | \DateTime $activeTil
    *
    * bigint
    * @param null | int $duration
    *
    * text
    * @param string $data
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param int[] $slotProviders
    *
    * 
    * @param \OpenAPI\Server\Model\FreeSpin $freespin
    *
    * 
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $triggerSessions
    *
    * 
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $activeTriggerSessions
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $type = 'wager',
        public string $bonuses,
        public string $maxTransfers,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public bool $crash = false,
        public int $totalTransferred,
        public int $uses,
        public int $transfers,
        public int $totalUses,
        public bool $isExternal = false,
        public bool $isPromo = false,
        public string $data,
        public array $slotProviders,
        public \OpenAPI\Server\Model\FreeSpin $freespin,
        public array $triggerSessions,
        public array $activeTriggerSessions,
        public ?string $bonusName = null,
        public ?string $description = null,
        public ?string $condition = null,
        public ?string $image = null,
        public ?string $depositFactors = null,
        public ?int $minDeposit = null,
        public ?float $minFactor = null,
        public ?int $minBet = null,
        public ?\DateTime $activeFrom = null,
        public ?\DateTime $activeTil = null,
        public ?int $duration = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

