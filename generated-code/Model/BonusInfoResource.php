<?php
declare(strict_types=1);


/**
 * BonusInfoResource
 */
namespace OpenAPI\Server\Model;

/**
 * BonusInfoResource
 */
use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfoResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * datetime
    * @param null | int $visibleFrom
    *
    * datetime
    * @param null | int $visibleTo
    *
    * datetime
    * @param null | int $timeFrom
    *
    * datetime
    * @param null | int $timerTo
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param null | bool $isWelcome
    *
    * boolean
    * @param bool $isForSmartico
    *
    * string(255)
    * @param null | string $image
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param object $description
    *
    * 
    * @param object $descriptionInfo
    *
    * 
    * @param object $conditionTitle
    *
    * 
    * @param object $condition
    *
    * 
    * @param null | string[] $colors
    *
    * 
    * @param \OpenAPI\Server\Model\BonusInfoResourceBonus $bonus
    *
    * boolean
    * @param bool $showVisibleDate
    *
    * boolean
    * @param null | bool $customType
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $currency,
        public bool $active,
        public bool $casino,
        public bool $isForSmartico = false,
        public object $bonusName,
        public object $description,
        public object $descriptionInfo,
        public object $conditionTitle,
        public object $condition,
        public \OpenAPI\Server\Model\BonusInfoResourceBonus $bonus,
        public bool $showVisibleDate = false,
        public ?int $visibleFrom = null,
        public ?int $visibleTo = null,
        public ?int $timeFrom = null,
        public ?int $timerTo = null,
        public ?bool $isWelcome = false,
        public ?string $image = null,
        public ?array $colors = null,
        public ?bool $customType = null,
    ) {}
}

