<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * BonusPlayer
 */
namespace OpenAPI\Server\Model;

/**
 * BonusPlayer
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayer
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonusId
    *
    * bigint
    * @param int $playerId
    *
    * bigint
    * @param null | int $triggerSessionId
    *
    * string(255)
    * @param string $status
    *
    * integer
    * @param null | int $depositsCount
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public int $playerId,
        public string $status,
        public ?int $triggerSessionId = null,
        public ?int $depositsCount = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

