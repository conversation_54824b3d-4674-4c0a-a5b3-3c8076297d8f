<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * DataSubscriptions
 */
namespace OpenAPI\Server\Model;

/**
 * DataSubscriptions
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class DataSubscriptions
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $subId
    *
    * string(255)
    * @param string $accessToken
    *
    * string(255)
    * @param null | string $gameToken
    *
    * string(255)
    * @param string $provider
    *
    * string(255)
    * @param string $providerCompany
    *
    * boolean
    * @param bool $isApproved
    *
    * boolean
    * @param bool $isSlot
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $subId,
        public string $accessToken,
        public string $provider,
        public string $providerCompany,
        public bool $isApproved = false,
        public bool $isSlot = false,
        public ?string $gameToken = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

