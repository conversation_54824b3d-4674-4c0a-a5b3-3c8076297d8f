<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * FreeSpin
 */
namespace OpenAPI\Server\Model;

/**
 * FreeSpin
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpin
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $slotId
    *
    * bigint
    * @param int $providerId
    *
    * string(255)
    * @param null | string $bonusId
    *
    * string(255)
    * @param string $type
    *
    * string(50)
    * @param string $name
    *
    * integer
    * @param int $count
    *
    * integer
    * @param int $bet
    *
    * string(255)
    * @param null | string $betId
    *
    * string(255)
    * @param null | string $denomination
    *
    * string(3)
    * @param string $currency
    *
    * string(255)
    * @param string $status
    *
    * boolean
    * @param bool $isActive
    *
    * bigint
    * @param int $authorId
    *
    * bigint
    * @param null | int $updatedByAuthorId
    *
    * boolean
    * @param bool $inProcess
    *
    * datetime
    * @param null | \DateTime $startAt
    *
    * datetime
    * @param null | \DateTime $expiredAt
    *
    * json
    * @param null | string $data
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Server\Model\FreeSpinBound[] $bounds
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    *
    * 
    * @param \OpenAPI\Server\Model\User $author
    *
    * 
    * @param \OpenAPI\Server\Model\User $updateBy
    */

    public function __construct(
        public int $id,
        public int $slotId,
        public int $providerId,
        public string $type = 'manual',
        public string $name,
        public int $count,
        public int $bet,
        public string $currency,
        public string $status = 'active',
        public bool $isActive = false,
        public int $authorId,
        public bool $inProcess = false,
        public array $bounds,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public \OpenAPI\Server\Model\User $author,
        public \OpenAPI\Server\Model\User $updateBy,
        public ?string $bonusId = null,
        public ?string $betId = null,
        public ?string $denomination = null,
        public ?int $updatedByAuthorId = null,
        public ?\DateTime $startAt = null,
        public ?\DateTime $expiredAt = null,
        public ?string $data = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

