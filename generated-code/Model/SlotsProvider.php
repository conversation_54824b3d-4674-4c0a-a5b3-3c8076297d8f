<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * SlotsProvider
 */
namespace OpenAPI\Server\Model;

/**
 * SlotsProvider
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class SlotsProvider
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * bigint
    * @param int $subscriptionId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param null | string $customName
    *
    * string(255)
    * @param null | string $image
    *
    * boolean
    * @param bool $suspended
    *
    * boolean
    * @param bool $isBonusBarred
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Server\Model\Slot[] $getProviders
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public int $subscriptionId,
        public string $name,
        public bool $suspended = false,
        public bool $isBonusBarred = false,
        public array $getProviders,
        public ?string $customName = null,
        public ?string $image = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

