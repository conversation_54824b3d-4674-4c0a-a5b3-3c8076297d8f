<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * PromoCode
 */
namespace OpenAPI\Server\Model;

/**
 * PromoCode
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCode
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $streamId
    *
    * bigint
    * @param int $bonusId
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $isActive
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $useLimit
    *
    * datetime
    * @param null | int $startAt
    *
    * datetime
    * @param null | int $endAt
    *
    * string
    * @param object $condition
    *
    * boolean
    * @param bool $isAlanbase
    *
    * string(36)
    * @param string $uuid
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $code,
        public int $bonusId,
        public bool $isActive = false,
        public int $uses,
        public int $useLimit,
        public object $condition,
        public bool $isAlanbase = false,
        public string $uuid,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public ?int $streamId = null,
        public ?string $description = null,
        public ?int $startAt = null,
        public ?int $endAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

