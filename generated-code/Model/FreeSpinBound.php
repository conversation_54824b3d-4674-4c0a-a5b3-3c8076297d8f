<?php
declare(strict_types=1);


/**
 * FreeSpinBound
 */
namespace OpenAPI\Server\Model;

/**
 * FreeSpinBound
 */
use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBound
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $freeSpinId
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonusId
    *
    * bigint
    * @param int $bet
    *
    * string(255)
    * @param string $betId
    *
    * integer
    * @param int $totalWin
    *
    * integer
    * @param int $countLeft
    *
    * boolean
    * @param bool $isActive
    *
    * boolean
    * @param bool $isArchived
    *
    * text(65535)
    * @param null | string $message
    *
    * datetime
    * @param null | float $startAt
    *
    * datetime
    * @param null | float $expireAt
    *
    * datetime
    * @param null | float $canceledAt
    *
    * boolean
    * @param bool $isUserNotified
    *
    * datetime
    * @param null | float $createdAt
    *
    * datetime
    * @param null | float $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\Player $playerInfo
    */

    public function __construct(
        public int $id,
        public int $freeSpinId,
        public string $uuid,
        public int $bet,
        public string $betId,
        public int $totalWin,
        public int $countLeft,
        public bool $isActive = true,
        public bool $isArchived = false,
        public bool $isUserNotified = false,
        public \OpenAPI\Server\Model\Player $playerInfo,
        public ?int $bonusId = null,
        public ?string $message = null,
        public ?float $startAt = null,
        public ?float $expireAt = null,
        public ?float $canceledAt = null,
        public ?float $createdAt = null,
        public ?float $updatedAt = null,
    ) {}
}

