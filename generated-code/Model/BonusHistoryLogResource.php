<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * BonusHistoryLogResource
 */
namespace OpenAPI\Server\Model;

/**
 * BonusHistoryLogResource
 */
use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusHistoryLogResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $bonusId
    *
    * 
    * @param string $type
    *
    * 
    * @param string $authorEmail
    *
    * 
    * @param string[] $data
    *
    * datetime
    * @param \DateTime $createdAt
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public string $type,
        public string $authorEmail,
        public array $data,
        public \DateTime $createdAt,
    ) {}
}

