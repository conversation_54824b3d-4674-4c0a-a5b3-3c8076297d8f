<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * BonusInfoResourceBonus
 */
namespace OpenAPI\Server\Model;

/**
 * BonusInfoResourceBonus
 */
use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfoResourceBonus
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param int[] $depositFactors
    *
    * 
    * @param null | int $maxBonuses
    */

    public function __construct(
        public int $id,
        public string $type = 'wager',
        public array $depositFactors,
        public ?int $maxBonuses = null,
    ) {}
}

