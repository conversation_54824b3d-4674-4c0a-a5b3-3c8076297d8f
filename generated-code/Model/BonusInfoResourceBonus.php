<?php
declare(strict_types=1);


/**
 * BonusInfoResourceBonus
 */
namespace OpenAPI\Server\Model;

/**
 * BonusInfoResourceBonus
 */
use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfoResourceBonus
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param int[] $depositFactors
    *
    * 
    * @param null | int $maxBonuses
    */

    public function __construct(
        public int $id,
        public string $type = 'wager',
        public array $depositFactors,
        public ?int $maxBonuses = null,
    ) {}
}

