<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * PaginationMeta
 */
namespace OpenAPI\Server\Model;

/**
 * PaginationMeta
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationMeta
{
    /**
    *
    * Current page
    * @param int $currentPage
    *
    * From
    * @param int $from
    *
    * Last page
    * @param int $lastPage
    *
    * 
    * @param \OpenAPI\Server\Model\PaginationLinksInner[] $links
    *
    * Path
    * @param string $path
    *
    * Items per page
    * @param int $perPage
    *
    * Total pages
    * @param int $to
    *
    * Total items
    * @param int $total
    */

    public function __construct(
        public int $currentPage = 1,
        public int $from = 1,
        public int $lastPage = 11,
        public array $links,
        public string $path = 'http://localhost:8000/api/bonuses',
        public int $perPage = 15,
        public int $to,
        public int $total,
    ) {}
}

