<?php
declare(strict_types=1);


/**
 * PaginationMeta
 */
namespace OpenAPI\Server\Model;

/**
 * PaginationMeta
 */
use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationMeta
{
    /**
    *
    * Current page
    * @param int $currentPage
    *
    * From
    * @param int $from
    *
    * Last page
    * @param int $lastPage
    *
    * 
    * @param \OpenAPI\Server\Model\PaginationLinksInner[] $links
    *
    * Path
    * @param string $path
    *
    * Items per page
    * @param int $perPage
    *
    * Total pages
    * @param int $to
    *
    * Total items
    * @param int $total
    */

    public function __construct(
        public int $currentPage = 1,
        public int $from = 1,
        public int $lastPage = 11,
        public array $links,
        public string $path = 'http://localhost:8000/api/bonuses',
        public int $perPage = 15,
        public int $to,
        public int $total,
    ) {}
}

