<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * BonusResource
 */
namespace OpenAPI\Server\Model;

/**
 * BonusResource
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $weight
    *
    * string(255)
    * @param string $name
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param string $type
    *
    * 
    * @param null | string $image
    *
    * 
    * @param int[] $maxBonuses
    *
    * 
    * @param int[] $maxTransfers
    *
    * 
    * @param int[] $depositFactors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $minDeposit
    *
    * float
    * @param null | float $minFactor
    *
    * bigint
    * @param null | int $minBet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * boolean
    * @param bool $crash
    *
    * bigint
    * @param int $totalTransferred
    *
    * integer
    * @param int $uses
    *
    * bigint
    * @param int $transfers
    *
    * bigint
    * @param int $totalUses
    *
    * boolean
    * @param bool $isExternal
    *
    * boolean
    * @param bool $isPromo
    *
    * datetime
    * @param null | \DateTime $from
    *
    * datetime
    * @param null | \DateTime $to
    *
    * bigint
    * @param null | int $duration
    *
    * 
    * @param object $bonusData
    *
    * boolean
    * @param bool $isWelcome
    *
    * boolean
    * @param bool $isOnetime
    *
    * boolean
    * @param bool $isNoDep
    *
    * boolean
    * @param bool $isDeposit
    *
    * boolean
    * @param bool $isFreeSpinForDeposit
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param int[] $slotProviders
    *
    * 
    * @param int[] $slots
    *
    * 
    * @param \OpenAPI\Server\Model\FreeSpin $freespin
    *
    * 
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $triggerSessions
    *
    * 
    * @param null | int $maxRealBalance
    *
    * 
    * @param null | int $genreId
    */

    public function __construct(
        public int $id,
        public int $weight,
        public string $name,
        public object $bonusName,
        public object $description,
        public object $condition,
        public string $type = 'wager',
        public array $maxBonuses,
        public array $maxTransfers,
        public array $depositFactors,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public bool $crash = false,
        public int $totalTransferred,
        public int $uses,
        public int $transfers,
        public int $totalUses,
        public bool $isExternal = false,
        public bool $isPromo = false,
        public object $bonusData,
        public bool $isWelcome,
        public bool $isOnetime,
        public bool $isNoDep,
        public bool $isDeposit,
        public bool $isFreeSpinForDeposit,
        public array $slotProviders,
        public array $slots,
        public \OpenAPI\Server\Model\FreeSpin $freespin,
        public array $triggerSessions,
        public ?string $image = null,
        public ?int $minDeposit = null,
        public ?float $minFactor = null,
        public ?int $minBet = null,
        public ?\DateTime $from = null,
        public ?\DateTime $to = null,
        public ?int $duration = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?int $maxRealBalance = null,
        public ?int $genreId = null,
    ) {}
}

