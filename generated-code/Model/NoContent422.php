<?php
declare(strict_types=1);


/**
 * NoContent422
 */
namespace OpenAPI\Server\Model;

/**
 * NoContent422
 * @description No content for 422
 */
use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class NoContent422
{
    /**
    *
    * dummy property for no-content responses
    * @param null | string $dummy
    */

    public function __construct(
        public ?string $dummy = null,
    ) {}
}

