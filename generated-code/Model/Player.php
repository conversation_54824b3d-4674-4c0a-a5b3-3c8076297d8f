<?php declare(strict_types=1);

/**
 * <PERSON>vel
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * Player
 */
namespace OpenAPI\Server\Model;

/**
 * Player
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Player
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param null | string $username
    *
    * string(255)
    * @param string $password
    *
    * string(255)
    * @param null | string $email
    *
    * boolean
    * @param bool $isUnderModeration
    *
    * boolean
    * @param bool $payoutWithoutModeration
    *
    * boolean
    * @param bool $subscribed
    *
    * integer
    * @param null | int $bonusBalanceId
    *
    * string(36)
    * @param string $uuid
    *
    * datetime
    * @param null | \DateTime $emailVerifiedAt
    *
    * string(255)
    * @param null | string $phone
    *
    * string(6)
    * @param null | string $phoneCode
    *
    * datetime
    * @param null | \DateTime $phoneVerifiedAt
    *
    * string(255)
    * @param string $firstName
    *
    * string(255)
    * @param null | string $lastName
    *
    * date
    * @param null | \DateTime $birth
    *
    * string(2)
    * @param null | string $country
    *
    * string(255)
    * @param null | string $city
    *
    * string(7)
    * @param null | string $gender
    *
    * string(2)
    * @param null | string $language
    *
    * string(255)
    * @param null | string $clickId
    *
    * string(3)
    * @param string $currency
    *
    * string(45)
    * @param null | string $lastSeenIp
    *
    * boolean
    * @param bool $blocked
    *
    * text(65535)
    * @param null | string $comment
    *
    * integer
    * @param null | int $welcomeBonusId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $password,
        public bool $isUnderModeration = false,
        public bool $payoutWithoutModeration = false,
        public bool $subscribed = false,
        public string $uuid,
        public string $firstName,
        public string $currency,
        public bool $blocked = false,
        public ?string $username = null,
        public ?string $email = null,
        public ?int $bonusBalanceId = null,
        public ?\DateTime $emailVerifiedAt = null,
        public ?string $phone = null,
        public ?string $phoneCode = null,
        public ?\DateTime $phoneVerifiedAt = null,
        public ?string $lastName = null,
        public ?\DateTime $birth = null,
        public ?string $country = null,
        public ?string $city = null,
        public ?string $gender = null,
        public ?string $language = null,
        public ?string $clickId = null,
        public ?string $lastSeenIp = null,
        public ?string $comment = null,
        public ?int $welcomeBonusId = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

