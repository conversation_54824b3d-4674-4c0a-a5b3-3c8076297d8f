<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * PaginationFirstAndLastLinks
 */
namespace OpenAPI\Server\Model;

/**
 * PaginationFirstAndLastLinks
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationFirstAndLastLinks
{
    /**
    *
    * First
    * @param string $first
    *
    * Last
    * @param string $last
    *
    * Previous
    * @param null | string $prev
    *
    * Next
    * @param string $next
    */

    public function __construct(
        public string $first,
        public string $last,
        public string $next,
        public ?string $prev = null,
    ) {}
}

