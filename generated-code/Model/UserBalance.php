<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * UserBalance
 */
namespace OpenAPI\Server\Model;

/**
 * UserBalance
 */
use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UserBalance
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $playerId
    *
    * string(3)
    * @param string $currency
    *
    * integer
    * @param null | int $lastTransactionId
    *
    * bigint
    * @param int $balance
    *
    * bigint
    * @param int $debt
    *
    * bigint
    * @param int $deposit
    *
    * bigint
    * @param int $wager
    *
    * bigint
    * @param int $withdraw
    *
    * bigint
    * @param int $inGame
    *
    * bigint
    * @param int $currentPayoutLimit
    *
    * bigint
    * @param int $totalBet
    *
    * bigint
    * @param int $totalProfit
    *
    * bigint
    * @param int $payoutApproved
    *
    * bigint
    * @param int $payoutWait
    */

    public function __construct(
        public int $id,
        public int $playerId,
        public string $currency,
        public int $balance,
        public int $debt,
        public int $deposit,
        public int $wager,
        public int $withdraw,
        public int $inGame,
        public int $currentPayoutLimit,
        public int $totalBet,
        public int $totalProfit,
        public int $payoutApproved,
        public int $payoutWait,
        public ?int $lastTransactionId = null,
    ) {}
}

