<?php
declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


/**
 * BonusTriggerSession
 */
namespace OpenAPI\Server\Model;

/**
 * BonusTriggerSession
 */
use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusTriggerSession
{
    /**
    *
    * datetime
    * @param \DateTime $startAt
    *
    * datetime
    * @param \DateTime $endAt
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public \DateTime $startAt,
        public \DateTime $endAt,
        public ?string $uuid = null,
    ) {}
}

