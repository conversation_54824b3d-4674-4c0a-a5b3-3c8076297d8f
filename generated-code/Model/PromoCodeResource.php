<?php
declare(strict_types=1);


/**
 * PromoCodeResource
 */
namespace OpenAPI\Server\Model;

/**
 * PromoCodeResource
 */
use <PERSON>rell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCodeResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $streamId
    *
    * bigint
    * @param int $bonusId
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $active
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $limit
    *
    * datetime
    * @param null | int $startAt
    *
    * datetime
    * @param null | int $endAt
    *
    * 
    * @param object $condition
    *
    * boolean
    * @param bool $isAlanbase
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param string $uuid
    *
    * 
    * @param \OpenAPI\Server\Model\BonusResource $bonus
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $code,
        public int $bonusId,
        public bool $active = false,
        public int $uses,
        public int $limit,
        public object $condition,
        public bool $isAlanbase = false,
        public string $uuid,
        public \OpenAPI\Server\Model\BonusResource $bonus,
        public ?int $streamId = null,
        public ?string $description = null,
        public ?int $startAt = null,
        public ?int $endAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

