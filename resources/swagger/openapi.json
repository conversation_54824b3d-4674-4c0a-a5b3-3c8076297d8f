{"openapi": "3.0.0", "info": {"title": "<PERSON><PERSON>", "description": "Documentation for the Application API", "version": "1.0.0"}, "servers": [{"url": "http://bonus-management.whitelabel.dd/api/", "description": "Laravel Server #1"}], "paths": {"/api/bonuses/add": {"post": {"summary": "", "description": "create bonus", "operationId": "createBonus", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BonusResource"}}}}, "401": {"description": "(Unauthorized) Invalid or missing Access Token"}}, "parameters": [{"in": "header", "name": "X-Signature", "description": "", "required": false, "schema": {"type": "string"}}, {"in": "header", "name": "x-cluster-connection", "description": "", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["name", "type", "weight"], "properties": {"weight": {"type": "number", "example": 1, "description": "1 - 999", "minLength": 1, "maxLength": 999}, "name": {"type": "string", "example": "Bonus123"}, "image": {"type": "string", "example": "https://example.com/storage/123456/img.png"}, "description": {"$ref": "#/components/schemas/SimpleObject"}, "condition": {"$ref": "#/components/schemas/SimpleObject"}, "bonus_name": {"$ref": "#/components/schemas/SimpleObject"}, "type": {"type": "string", "enum": ["welcome", "welcome_4_steps", "onetime", "no_dep", "deposit", "freespin_bonus", "free_spins_for_deposit", "wager", "freebet", "free_money", "bet_refund", "no_risk"], "example": "onetime"}, "min_bet": {"type": "integer", "nullable": true, "minLength": 1, "example": 100}, "min_deposit": {"type": "integer", "nullable": true, "minLength": 1, "example": 1000}, "max_transfers": {"$ref": "#/components/schemas/ArrayOfInt"}, "max_bonuses": {"$ref": "#/components/schemas/ArrayOfInt"}, "deposit_factors": {"$ref": "#/components/schemas/ArrayOfInt"}, "duration": {"type": "integer", "nullable": true, "example": 60800}, "wager": {"type": "number", "nullable": true, "example": 1.0}, "currency": {"type": "string", "nullable": true, "example": "INR"}, "active": {"type": "boolean", "nullable": true, "example": true}, "casino": {"type": "boolean", "nullable": true, "example": true}, "bets": {"type": "boolean", "nullable": true, "example": false}, "from": {"type": "string", "nullable": true, "example": **********}, "to": {"type": "string", "nullable": true, "example": **********}, "min_factor": {"type": "number", "nullable": true, "example": 1}, "data": {"$ref": "#/components/schemas/SimpleObject"}, "providers_ids": {"$ref": "#/components/schemas/ArrayOfInt"}, "slots_ids": {"$ref": "#/components/schemas/ArrayOfInt"}, "trigger_sessions": {"type": "array", "description": "required if type is free_spins_for_deposit", "items": {"$ref": "#/components/schemas/BonusTriggerSession"}}, "segment_id": {"type": "integer", "nullable": true, "example": 1}, "is_promo": {"type": "boolean", "nullable": true, "example": true}, "is_external": {"type": "boolean", "nullable": true, "example": true}, "author_id": {"type": "integer", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "slots_id": {"type": "integer", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "provider_id": {"type": "integer", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "bonus_balance": {"type": "boolean", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": true}, "count": {"type": "integer", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "bet": {"type": "number", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "bet_id": {"type": "string", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": "1"}, "denomination": {"type": "number", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "max_real_balance": {"type": "integer", "nullable": true, "example": 1, "maximum": 999999, "minimum": 1}, "genre_id": {"type": "integer", "nullable": true, "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "example": 2}}}}}}, "tags": ["bonuses"]}}, "/api/bonuses/{id}": {"put": {"summary": "", "description": "update bonus", "operationId": "updateBonus", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BonusResource"}}}}, "401": {"description": "(Unauthorized) Invalid or missing Access Token"}}, "parameters": [{"in": "header", "name": "X-Signature", "description": "", "required": false, "schema": {"type": "string"}}, {"in": "header", "name": "x-cluster-connection", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "description": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["name", "type"], "properties": {"name": {"type": "string", "example": "Bonus123"}, "image": {"type": "string", "example": "https://example.com/storage/123456/img.png"}, "description": {"$ref": "#/components/schemas/SimpleObject"}, "condition": {"$ref": "#/components/schemas/SimpleObject"}, "bonus_name": {"$ref": "#/components/schemas/SimpleObject"}, "type": {"type": "string", "enum": ["welcome", "welcome_4_steps", "onetime", "no_dep", "deposit", "freespin_bonus", "free_spins_for_deposit", "wager", "freebet", "free_money", "bet_refund", "no_risk"], "example": "onetime"}, "min_bet": {"type": "integer", "nullable": true, "minLength": 1, "example": 100}, "min_deposit": {"type": "integer", "nullable": true, "minLength": 1, "example": 1000}, "max_transfers": {"$ref": "#/components/schemas/ArrayOfInt"}, "max_bonuses": {"$ref": "#/components/schemas/ArrayOfInt"}, "deposit_factors": {"$ref": "#/components/schemas/ArrayOfInt"}, "duration": {"type": "integer", "nullable": true, "example": 60800}, "wager": {"type": "number", "nullable": true, "example": 1.0}, "currency": {"type": "string", "nullable": true, "example": "INR"}, "active": {"type": "boolean", "nullable": true, "example": true}, "casino": {"type": "boolean", "nullable": true, "example": true}, "bets": {"type": "boolean", "nullable": true, "example": false}, "from": {"type": "string", "nullable": true, "example": **********}, "to": {"type": "string", "nullable": true, "example": **********}, "min_factor": {"type": "number", "nullable": true, "example": 1}, "data": {"$ref": "#/components/schemas/SimpleObject"}, "providers_ids": {"$ref": "#/components/schemas/ArrayOfInt"}, "slots_ids": {"$ref": "#/components/schemas/ArrayOfInt"}, "trigger_sessions": {"type": "array", "description": "required if type is free_spins_for_deposit", "items": {"$ref": "#/components/schemas/BonusTriggerSession"}}, "segment_id": {"type": "integer", "nullable": true, "example": 1}, "is_promo": {"type": "boolean", "nullable": true, "example": true}, "is_external": {"type": "boolean", "nullable": true, "example": true}, "author_id": {"type": "integer", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "slots_id": {"type": "integer", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "provider_id": {"type": "integer", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "bonus_balance": {"type": "boolean", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": true}, "count": {"type": "integer", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "bet": {"type": "number", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "bet_id": {"type": "string", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": "1"}, "denomination": {"type": "number", "description": "required if type is free_spins_for_deposit", "nullable": true, "example": 1}, "max_real_balance": {"type": "integer", "nullable": true, "example": 1, "maximum": 999999, "minimum": 1}, "genre_id": {"type": "integer", "nullable": true, "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "example": 2}}}}}}, "tags": ["bonuses"]}, "delete": {"summary": "", "description": "delete bonus", "operationId": "deleteBonus", "responses": {"200": {"description": "OK"}, "401": {"description": "(Unauthorized) Invalid or missing Access Token"}}, "parameters": [{"in": "header", "name": "X-Signature", "description": "", "required": false, "schema": {"type": "string"}}, {"in": "header", "name": "x-cluster-connection", "description": "", "required": false, "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "description": "", "schema": {"type": "string"}}], "tags": ["bonuses"]}}}, "tags": [], "components": {"schemas": {"BannerResource": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "Banner123"}, "language": {"type": "string", "description": "string(2)", "nullable": true, "example": "en"}, "country": {"type": "string", "description": "string(3)", "nullable": true, "example": "FR"}, "is_for_signed_in": {"type": "string", "description": "string(255)", "nullable": false, "example": "all"}, "platform": {"type": "string", "description": "string(255)", "nullable": false, "example": "all"}, "mobile_apk_install": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "location": {"type": "string", "description": "string(255)", "nullable": false, "example": "https://example.com"}, "weight": {"type": "integer", "default": 0, "description": "bigint", "nullable": false, "example": 1}, "type": {"type": "string", "description": "string(255)", "nullable": false, "enum": ["big", "small", "upper"], "example": "big"}, "disposition": {"$ref": "#/components/schemas/SimpleObject"}, "image": {"type": "string", "description": "string(255)", "nullable": false, "example": "https://example.com/images/image.jpg"}, "enabled": {"type": "boolean", "description": "boolean", "nullable": false, "example": true}, "is_active": {"type": "boolean", "description": "boolean", "nullable": false, "example": true}, "smartico_segment_id": {"type": "integer", "description": "integer", "nullable": true, "example": 1}, "start_at": {"type": "integer", "description": "integer", "nullable": true, "example": 1717794000}, "end_at": {"type": "integer", "description": "integer", "nullable": true, "example": 1717794000}, "uuid": {"type": "string", "description": "string(255)", "nullable": true, "example": "d2550375-ee43-4e2d-972f-26be72f4509e"}}}, "Bonus": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1935783063}, "client_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1482898742}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "bonus_name": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "description": {"type": "string", "description": "text", "nullable": true, "example": "string"}, "condition": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "type": {"type": "string", "description": "string(40)", "default": "wager", "nullable": false, "example": "string"}, "image": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "bonuses": {"type": "string", "description": "json", "nullable": false, "example": "string"}, "max_transfers": {"type": "string", "description": "json", "nullable": false, "example": "string"}, "deposit_factors": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "string"}, "wager": {"type": "number", "format": "double", "description": "decimal", "nullable": false}, "min_deposit": {"type": "integer", "description": "integer", "nullable": true, "example": 1818542117}, "min_factor": {"type": "number", "format": "float", "description": "float", "nullable": true}, "min_bet": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1593646809}, "active": {"type": "boolean", "description": "boolean", "nullable": false, "example": true}, "casino": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "bets": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "crash": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": true}, "total_transferred": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1698833655}, "uses": {"type": "integer", "description": "integer", "default": "0", "nullable": false, "example": 1130019927}, "transfers": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1864316519}, "total_uses": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1381174703}, "is_external": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "is_promo": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "active_from": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "active_til": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "duration": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": **********}, "data": {"type": "string", "description": "text", "nullable": false, "example": "string"}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "uuid": {"type": "string", "description": "string(36)", "nullable": true, "example": "string"}, "slot_providers": {"type": "array", "items": {"type": "integer"}}, "freespin": {"$ref": "#/components/schemas/FreeSpin"}, "trigger_sessions": {"type": "array", "items": {"$ref": "#/components/schemas/BonusTriggerSession"}}, "active_trigger_sessions": {"type": "array", "items": {"$ref": "#/components/schemas/BonusTriggerSession"}}}, "required": ["id", "client_id", "name", "type", "bonuses", "max_transfers", "currency", "wager", "active", "casino", "bets", "crash", "total_transferred", "uses", "transfers", "total_uses", "is_external", "is_promo", "data"]}, "BonusResource": {"type": "object", "properties": {"id": {"type": "integer", "nullable": false, "example": 1}, "weight": {"type": "integer", "nullable": false, "example": 1, "minimum": 1, "maximum": 999}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "bonus123"}, "bonus_name": {"$ref": "#/components/schemas/SimpleObject"}, "description": {"$ref": "#/components/schemas/SimpleObject"}, "condition": {"$ref": "#/components/schemas/SimpleObject"}, "type": {"type": "string", "default": "wager", "nullable": false, "example": "welcome_4_steps"}, "image": {"type": "string", "nullable": true, "example": "https://example.com/storage/123456/img.jpeg"}, "max_bonuses": {"$ref": "#/components/schemas/ArrayOfInt"}, "max_transfers": {"$ref": "#/components/schemas/ArrayOfInt"}, "deposit_factors": {"$ref": "#/components/schemas/ArrayOfInt"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "INR"}, "wager": {"type": "number", "format": "double", "description": "decimal", "nullable": false, "example": 5.0}, "min_deposit": {"type": "integer", "description": "integer", "nullable": true, "example": 5000}, "min_factor": {"type": "number", "format": "float", "description": "float", "nullable": true}, "min_bet": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1000}, "active": {"type": "boolean", "description": "boolean", "nullable": false, "example": 1}, "casino": {"type": "boolean", "description": "boolean", "nullable": false, "example": 1}, "bets": {"type": "boolean", "description": "boolean", "nullable": false, "example": 0}, "crash": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": 1}, "total_transferred": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 0}, "uses": {"type": "integer", "description": "integer", "default": "0", "nullable": false, "example": 0}, "transfers": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 0}, "total_uses": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 0}, "is_external": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": 0}, "is_promo": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": 0}, "from": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-04-30T21:00:00.000000Z"}, "to": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-05-30T21:00:00.000000Z"}, "duration": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 604800}, "bonus_data": {"$ref": "#/components/schemas/SimpleObject"}, "is_welcome": {"type": "boolean", "description": "boolean", "example": false}, "is_onetime": {"type": "boolean", "description": "boolean", "example": true}, "is_no_dep": {"type": "boolean", "description": "boolean", "example": false}, "is_deposit": {"type": "boolean", "description": "boolean", "example": false}, "is_free_spin_for_deposit": {"type": "boolean", "description": "boolean", "example": false}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "**********"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "**********"}, "slot_providers": {"type": "array", "items": {"type": "integer"}}, "slots": {"items": {"type": "integer"}}, "freespin": {"$ref": "#/components/schemas/FreeSpin"}, "trigger_sessions": {"type": "array", "items": {"$ref": "#/components/schemas/BonusTriggerSession"}}, "max_real_balance": {"type": "integer", "format": "int64", "nullable": true, "default": null, "example": 125}, "genre_id": {"type": "integer", "nullable": true, "example": 2}}}, "BonusHistoryLogResource": {"type": "object", "properties": {"id": {"type": "integer", "nullable": false, "example": 1}, "bonus_id": {"type": "integer", "nullable": false, "example": "<EMAIL>"}, "type": {"type": "string", "nullable": false, "example": "update"}, "author_email": {"type": "string", "nullable": false, "example": 1}, "data": {"type": "array", "nullable": false, "example": {"name": {"current": "testBonus1223343", "previous": "testBonus12233"}, "bonus_name": {"en": {"current": "<p>xsadas </p>", "previous": "<p>xsadas</p>"}}}}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": false, "example": **********}}}, "FreeSpinHistoryLogResource": {"type": "object", "properties": {"id": {"type": "integer", "nullable": false, "example": 1}, "free_spin_id": {"type": "integer", "nullable": false, "example": 1}, "type": {"type": "string", "nullable": false, "example": "update"}, "author_email": {"type": "string", "nullable": false, "example": "<EMAIL>"}, "data": {"type": "array", "nullable": false, "example": {"name": {"current": "testBonus1223343", "previous": "testBonus12233"}, "bonus_name": {"en": {"current": "<p>xsadas </p>", "previous": "<p>xsadas</p>"}}}}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": false, "example": **********}}}, "BonusForPlayerResource": {"type": "object", "properties": {"id": {"type": "integer", "nullable": false, "example": 1}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "bonus123"}, "bonus_name": {"$ref": "#/components/schemas/SimpleObject"}, "description": {"$ref": "#/components/schemas/SimpleObject"}, "condition": {"$ref": "#/components/schemas/SimpleObject"}, "type": {"type": "string", "default": "wager", "nullable": false, "example": "welcome_4_steps"}, "image": {"type": "string", "nullable": true, "example": "https://example.com/storage/123456/img.jpeg"}, "crash": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": 1}, "max_bonus": {"type": "object", "properties": {"amount": {"type": "string", "nullable": false, "example": "100000000"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "INR"}}}, "max_bonuses": {"$ref": "#/components/schemas/ArrayOfInt"}, "max_transfers": {"$ref": "#/components/schemas/ArrayOfInt"}, "deposit_factors": {"$ref": "#/components/schemas/ArrayOfInt"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "INR"}, "wager": {"type": "number", "format": "double", "description": "decimal", "nullable": false, "example": 5.0}, "min_deposit": {"type": "integer", "description": "integer", "nullable": true, "example": 5000}, "min_bet": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1000}, "active": {"type": "boolean", "description": "boolean", "nullable": false, "example": 1}, "casino": {"type": "boolean", "description": "boolean", "nullable": false, "example": 1}, "bets": {"type": "boolean", "description": "boolean", "nullable": false, "example": 0}, "from": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-04-30T21:00:00.000000Z"}, "to": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-05-30T21:00:00.000000Z"}, "duration": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 604800}, "bonus_data": {"$ref": "#/components/schemas/SimpleObject"}, "is_welcome": {"type": "boolean", "description": "boolean", "example": false}, "is_onetime": {"type": "boolean", "description": "boolean", "example": true}, "is_no_dep": {"type": "boolean", "description": "boolean", "example": false}, "is_deposit": {"type": "boolean", "description": "boolean", "example": false}, "is_free_spin_for_deposit": {"type": "boolean", "description": "boolean", "example": false}, "weight": {"type": "integer", "nullable": false, "example": 1, "minimum": 1, "maximum": 999}, "is_organic": {"type": "boolean", "description": "boolean", "example": false}, "triggerSessions": {"type": "array", "items": {"$ref": "#/components/schemas/BonusTriggerSession"}}}}, "BonusBalance": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1901514244}, "log_key": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1819031074}, "type": {"type": "string", "description": "string(40)", "default": "wager", "nullable": false, "example": "string"}, "player_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1622132139}, "balance": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1357923192}, "bonus_external_id": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "status": {"type": "string", "description": "string(40)", "nullable": false, "example": "string"}, "orig_bonus": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1552434658}, "orig_wager": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1259949272}, "wager": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1129078396}, "min_factor": {"type": "number", "format": "float", "description": "float", "nullable": true}, "min_bet": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1841501736}, "expire_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "in_game": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1131047579}, "transfer": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1575460513}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "string"}, "active": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "popup_seen": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "casino": {"type": "boolean", "description": "boolean", "nullable": false, "example": true}, "bets": {"type": "boolean", "description": "boolean", "nullable": false, "example": true}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "player": {"$ref": "#/components/schemas/User"}, "bonus": {"$ref": "#/components/schemas/Bonus"}}, "required": ["id", "bonus_id", "type", "player_id", "balance", "status", "orig_bonus", "orig_wager", "wager", "in_game", "transfer", "currency", "active", "popup_seen", "casino", "bets"]}, "BonusInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1114024775}, "client_id": {"type": "integer", "description": "integer", "nullable": false, "example": 1855020166}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "string"}, "active": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "visible_from": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "visible_to": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "casino": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "is_welcome": {"type": "boolean", "description": "boolean", "default": "0", "nullable": true, "example": true}, "is_for_smartico": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "image": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "bonus_name": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "description": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "description_info": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "condition_title": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "condition": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "colors": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "sort_order": {"type": "integer", "description": "integer", "nullable": false, "example": 1264300281}, "proceed_link": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1621525374}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "uuid": {"type": "string", "description": "string(36)", "nullable": true, "example": "string"}, "bonus": {"$ref": "#/components/schemas/Bonus"}}, "required": ["id", "client_id", "name", "currency", "active", "casino", "is_for_smartico", "sort_order"]}, "BonusInfoResource": {"type": "object", "required": ["id", "client_id", "name", "currency", "active", "casino", "is_for_smartico", "sort_order"], "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "BonusInfo123"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "INR"}, "active": {"type": "boolean", "description": "boolean", "nullable": false, "example": true}, "visible_from": {"type": "integer", "format": "date-time", "description": "datetime", "nullable": true, "example": 1698782400}, "visible_to": {"type": "integer", "format": "date-time", "description": "datetime", "nullable": true, "example": 1701288000}, "time_from": {"type": "integer", "format": "date-time", "description": "datetime", "nullable": true, "example": 1698782400}, "timer_to": {"type": "integer", "format": "date-time", "description": "datetime", "nullable": true, "example": 1701288000}, "casino": {"type": "boolean", "description": "boolean", "nullable": false, "example": true}, "is_welcome": {"type": "boolean", "description": "boolean", "default": "0", "nullable": true, "example": true}, "is_for_smartico": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "image": {"type": "string", "description": "string(255)", "nullable": true, "example": "https://example.com/images/image.jpg"}, "bonus_name": {"$ref": "#/components/schemas/SimpleObject"}, "description": {"$ref": "#/components/schemas/SimpleObject"}, "description_info": {"$ref": "#/components/schemas/SimpleObject"}, "condition_title": {"$ref": "#/components/schemas/SimpleObject"}, "condition": {"$ref": "#/components/schemas/SimpleObject"}, "colors": {"type": "array", "nullable": true, "default": {"gradient_1": "#000", "gradient_2": "#000", "gradient_3": "#000", "gradient_4": "#000", "shadows": "#000"}}, "bonus": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "type": {"type": "string", "description": "string(255)", "default": "wager", "nullable": false, "example": "welcome"}, "deposit_factors": {"$ref": "#/components/schemas/ArrayOfInt"}, "max_bonuses": {"type": "integer", "nullable": true, "example": 10000}}}, "show_visible_date": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": true}, "custom_type": {"type": "boolean", "description": "boolean", "nullable": true, "example": true}}}, "BonusPlayer": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1208583312}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1689260827}, "player_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1188463655}, "trigger_session_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1921551186}, "status": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "deposits_count": {"type": "integer", "description": "integer", "nullable": true, "example": 1088038885}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}}, "required": ["id", "bonus_id", "player_id", "status"]}, "BonusPlayerCard": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1455443716}, "player_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1273177660}, "is_used": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": true}, "bonus_id": {"type": "integer", "description": "integer", "nullable": true, "example": 1282789017}, "bonus_info_id": {"type": "integer", "description": "integer", "nullable": true, "example": 1457323109}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "player": {"$ref": "#/components/schemas/User"}, "bonus": {"$ref": "#/components/schemas/Bonus"}}, "required": ["id", "player_id", "is_used"]}, "BonusTriggerSession": {"type": "object", "properties": {"start_at": {"type": "string", "format": "date-time", "description": "datetime", "default": "CURRENT_TIMESTAMP", "nullable": false, "example": 1727384399}, "end_at": {"type": "string", "format": "date-time", "description": "datetime", "default": "CURRENT_TIMESTAMP", "nullable": false, "example": 1727384399}, "uuid": {"type": "string", "description": "string(36)", "nullable": true, "example": "string"}}, "required": ["start_at", "end_at"]}, "DataSubscriptions": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "client_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "sub_id": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "access_token": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "game_token": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "provider": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "provider_company": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "is_approved": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": true}, "is_slot": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": true}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "uuid": {"type": "string", "description": "string(36)", "nullable": true, "example": "string"}}, "required": ["id", "client_id", "sub_id", "access_token", "provider", "provider_company", "is_approved", "is_slot"]}, "DepositBonusInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "smartico_bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "player_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": **********}, "email": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "amount": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": **********}, "status": {"type": "string", "description": "string(255)", "default": "in_process", "nullable": false, "example": "string"}, "text": {"type": "string", "description": "text(65535)", "nullable": true, "example": "string"}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}}, "required": ["id", "bonus_id", "smartico_bonus_id", "status"]}, "FreeSpin": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "slot_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "provider_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "bonus_id": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "type": {"type": "string", "description": "string(255)", "default": "manual", "nullable": false, "example": "string"}, "name": {"type": "string", "description": "string(50)", "nullable": false, "example": "string"}, "count": {"type": "integer", "description": "integer", "nullable": false, "example": **********}, "bet": {"type": "integer", "description": "integer", "nullable": false, "example": **********}, "bet_id": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "denomination": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "string"}, "status": {"type": "string", "description": "string(255)", "default": "active", "nullable": false, "example": "string"}, "is_active": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": true}, "author_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1582128542}, "updated_by_author_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1039855775}, "in_process": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": false}, "start_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "expired_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "data": {"type": "string", "description": "json", "nullable": true, "example": "string"}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "uuid": {"type": "string", "description": "string(36)", "nullable": true, "example": "string"}, "bounds": {"type": "array", "items": {"$ref": "#/components/schemas/FreeSpinBound"}}, "bonus": {"$ref": "#/components/schemas/Bonus"}, "author": {"$ref": "#/components/schemas/User"}, "updateBy": {"$ref": "#/components/schemas/User"}}, "required": ["id", "slot_id", "provider_id", "type", "name", "count", "bet", "currency", "status", "is_active", "author_id", "in_process"]}, "FreeSpinResource": {"type": "object", "required": ["id", "slot_id", "provider_id", "general_provider_id", "type", "name", "product_name", "count", "bet", "currency", "status", "is_active", "author_id", "in_process"], "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "general_provider_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "aggregator_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1}, "slot_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "provider_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "type": {"type": "string", "description": "string(255)", "default": "manual", "nullable": false, "example": "manual"}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "FreeSpin123"}, "product_name": {"type": "string", "description": "string(255)", "nullable": false, "example": "Product123"}, "count": {"type": "integer", "description": "integer", "nullable": false, "example": 10}, "bet": {"type": "integer", "description": "integer", "nullable": false, "example": 10}, "bet_id": {"type": "string", "description": "string(255)", "default": "0", "example": "1"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "INR"}, "status": {"type": "string", "description": "string(255)", "default": "active", "example": "active"}, "denomination": {"type": "number", "description": "", "nullable": true, "example": 0.01}, "is_active": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": 1}, "author_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "updated_by_author_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1}, "in_process": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": false}, "start_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-08-26 21:00:00"}, "expired_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-09-25 20:59:59"}, "data": {"$ref": "#/components/schemas/SimpleObject"}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-08-27T14:07:07.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-08-27T14:07:07.000000Z"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/ArrayOfUserObjects"}}, "author": {"$ref": "#/components/schemas/User"}, "updateBy": {"$ref": "#/components/schemas/User"}}}, "FreeSpinBound": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "free_spin_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "uuid": {"type": "string", "description": "string(32)", "nullable": false, "example": "a900c3d6-76f8-4805-8225-ccb29f721a41"}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1}, "bet": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 100}, "bet_id": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "total_win": {"type": "integer", "description": "integer", "default": "0", "nullable": false, "example": 1000000}, "count_left": {"type": "integer", "description": "integer", "nullable": false, "example": 0}, "is_active": {"type": "boolean", "description": "boolean", "default": true, "nullable": false, "example": true}, "is_archived": {"type": "boolean", "description": "boolean", "default": false, "nullable": false, "example": false}, "message": {"type": "string", "description": "text(65535)", "nullable": true, "example": "string"}, "start_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707132568}, "expire_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1709240399}, "canceled_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1709240399}, "is_user_notified": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "created_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707130416}, "updated_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707420286}, "playerInfo": {"$ref": "#/components/schemas/Player"}}, "required": ["id", "free_spin_id", "uuid", "player_id", "bet_id", "bet", "total_win", "count_left", "is_active", "is_archived", "is_user_notified"]}, "FreeSpinBoundResource": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "free_spin_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "uuid": {"type": "string", "description": "string(32)", "nullable": false, "example": "a900c3d6-76f8-4805-8225-ccb29f721a41"}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1}, "bet": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 100}, "total_win": {"type": "integer", "description": "integer", "default": "0", "nullable": false, "example": 1000000}, "count_left": {"type": "integer", "description": "integer", "nullable": false, "example": 0}, "is_active": {"type": "boolean", "description": "boolean", "default": true, "nullable": false, "example": true}, "is_archived": {"type": "boolean", "description": "boolean", "default": false, "nullable": false, "example": false}, "start_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707132568}, "expire_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1709240399}, "canceled_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1709240399}, "created_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707130416}, "updated_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707420286}, "freespin": {"$ref": "#/components/schemas/FreeSpinResource"}, "slot_id": {"type": "integer"}, "bonus": {"$ref": "#/components/schemas/BonusResource"}}, "required": ["id", "free_spin_id", "uuid", "player_id", "bet_id", "bet", "total_win", "count_left", "is_active", "is_archived", "is_user_notified"]}, "FreeSpinBoundDataResource": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "free_spin_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "uuid": {"type": "string", "description": "string(32)", "nullable": false, "example": "a900c3d6-76f8-4805-8225-ccb29f721a41"}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1}, "bet_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 100}, "bet": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 100}, "total_win": {"type": "integer", "description": "integer", "default": "0", "nullable": false, "example": 1000000}, "count_left": {"type": "integer", "description": "integer", "nullable": false, "example": 0}, "message": {"type": "string", "description": "string", "nullable": false, "example": "test message"}, "is_active": {"type": "boolean", "description": "boolean", "default": true, "nullable": false, "example": true}, "is_archived": {"type": "boolean", "description": "boolean", "default": false, "nullable": false, "example": false}, "is_user_notified": {"type": "boolean", "description": "boolean", "default": false, "nullable": false, "example": false}, "start_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707132568}, "expire_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1709240399}, "canceled_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1709240399}, "created_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707130416}, "updated_at": {"type": "number", "format": "date-time", "description": "datetime", "nullable": true, "example": 1707420286}}, "required": ["id", "free_spin_id", "uuid", "player_id", "bet_id", "bet", "total_win", "count_left", "is_active", "is_archived", "is_user_notified"]}, "MassFreeBetBonusInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1263255976}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1797679046}, "smartico_bonus_id": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "template_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1871851641}, "external_player_id": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "currency": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "amount": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1685258893}, "status": {"type": "string", "description": "string(255)", "default": "in_process", "nullable": false, "example": "string"}, "text": {"type": "string", "description": "text(65535)", "nullable": true, "example": "string"}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "player_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1840392933}}, "required": ["id", "bonus_id", "currency", "status"]}, "MassOnetimeBonusInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1538359669}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1250553872}, "smartico_bonus_id": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "player_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1692611743}, "email": {"type": "string", "description": "string(255)", "nullable": true, "example": "string"}, "amount": {"type": "integer", "format": "int64", "description": "bigint", "nullable": true, "example": 1276103135}, "status": {"type": "string", "description": "string(255)", "default": "in_process", "nullable": false, "example": "string"}, "text": {"type": "string", "description": "text(65535)", "nullable": true, "example": "string"}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}}, "required": ["id", "bonus_id", "status"]}, "Player": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "client_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 2}, "username": {"type": "string", "description": "string(255)", "nullable": true, "example": "Player1"}, "password": {"type": "string", "description": "string(255)", "nullable": false, "example": "password"}, "email": {"type": "string", "description": "string(255)", "nullable": true, "example": "<EMAIL>"}, "is_under_moderation": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "payout_without_moderation": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": true}, "subscribed": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": true}, "bonus_balance_id": {"type": "integer", "description": "integer", "nullable": true, "example": 1}, "uuid": {"type": "string", "description": "string(36)", "nullable": false, "example": "4f11f3a4-936e-48ce-b87a-8fbcf6036459"}, "email_verified_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "phone": {"type": "string", "description": "string(255)", "nullable": true, "example": "+************"}, "phone_code": {"type": "string", "description": "string(6)", "nullable": true, "example": "IN"}, "phone_verified_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "first_name": {"type": "string", "description": "string(255)", "nullable": false, "example": "<PERSON>"}, "last_name": {"type": "string", "description": "string(255)", "nullable": true, "example": "<PERSON><PERSON>"}, "birth": {"type": "string", "format": "date", "description": "date", "nullable": true, "example": "2000-01-01"}, "country": {"type": "string", "description": "string(2)", "nullable": true, "example": "string"}, "city": {"type": "string", "description": "string(255)", "nullable": true, "example": "London"}, "gender": {"type": "string", "description": "string(7)", "nullable": true, "example": "male"}, "language": {"type": "string", "description": "string(2)", "nullable": true, "example": "en"}, "click_id": {"type": "string", "description": "string(255)", "nullable": true, "example": "1"}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "EUR"}, "last_seen_ip": {"type": "string", "description": "string(45)", "nullable": true, "example": "*************"}, "blocked": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "comment": {"type": "string", "description": "text(65535)", "nullable": true, "example": "any comment"}, "welcome_bonus_id": {"type": "integer", "description": "integer", "nullable": true, "example": 1}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-02-01T13:23:11.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-02-01T13:23:11.000000Z"}}, "required": ["id", "client_id", "password", "is_under_moderation", "payout_without_moderation", "subscribed", "uuid", "first_name", "currency", "blocked"]}, "PromoCode": {"type": "object", "properties": {"id": {"type": "integer", "description": "bigint", "nullable": false}, "client_id": {"type": "integer", "description": "bigint", "nullable": false}, "name": {"type": "string", "description": "string(255)", "nullable": false}, "code": {"type": "string", "description": "string(255)", "nullable": false}, "stream_id": {"type": "integer", "description": "bigint", "nullable": true}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false}, "description": {"type": "string", "description": "text", "nullable": true}, "is_active": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": true}, "uses": {"type": "integer", "description": "integer", "nullable": false}, "use_limit": {"type": "integer", "description": "integer", "nullable": false}, "start_at": {"type": "integer", "description": "datetime", "nullable": true}, "end_at": {"type": "integer", "description": "datetime", "nullable": true}, "condition": {"type": "object", "description": "string", "nullable": false}, "is_alanbase": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": true}, "uuid": {"type": "string", "description": "string(36)", "nullable": false, "example": "string"}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "bonus": {"$ref": "#/components/schemas/Bonus"}}, "required": ["id", "bonus_id", "name", "code", "condition"]}, "PromoCodeResource": {"type": "object", "properties": {"id": {"type": "integer", "description": "bigint", "nullable": false, "example": 1}, "client_id": {"type": "integer", "description": "bigint", "nullable": false, "example": 1}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "Promo123"}, "code": {"type": "string", "description": "string(255)", "nullable": false, "example": "PROMO123"}, "stream_id": {"type": "integer", "description": "bigint", "nullable": true, "example": 1}, "bonus_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "description": {"type": "string", "description": "text", "nullable": true, "example": "Promo description"}, "active": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": true}, "uses": {"type": "integer", "description": "integer", "nullable": false, "example": 1}, "limit": {"type": "integer", "description": "integer", "nullable": false, "example": 1}, "start_at": {"type": "integer", "description": "datetime", "nullable": true, "example": 0}, "end_at": {"type": "integer", "description": "datetime", "nullable": true, "example": 0}, "condition": {"$ref": "#/components/schemas/SimpleObject"}, "is_alanbase": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": true}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-07-18T11:29:02.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-07-18T11:29:02.000000Z"}, "uuid": {"type": "string", "description": "string(36)", "nullable": false, "example": "1bf3d2f2-17dc-4744-9577-94425b099ff4"}, "bonus": {"$ref": "#/components/schemas/BonusResource"}}}, "Slot": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "client_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "provider": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "version": {"type": "integer", "description": "integer", "default": "0", "nullable": false, "example": **********}, "external_provider_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": **********}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "slug": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "internal_id": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "external_id": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "enabled": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "suspended": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "meta": {"type": "string", "description": "text", "nullable": false, "example": "string"}, "image": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "description": {"type": "string", "description": "text(16777215)", "nullable": true, "example": "string"}, "is_mobile": {"type": "boolean", "description": "boolean", "nullable": false, "example": true}, "is_bonus_ready": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": false}, "is_wager_ready": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": true}, "is_desktop": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "has_lobby": {"type": "boolean", "description": "boolean", "nullable": false, "example": false}, "has_freespins": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": false}, "is_demo": {"type": "boolean", "description": "boolean", "nullable": true, "example": true}, "genre_id": {"type": "integer", "description": "integer", "default": "1", "nullable": false, "example": 1539766041}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "deleted_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "string"}, "friendly_url": {"type": "string", "description": "string(255)", "nullable": false, "example": "string"}, "uuid": {"type": "string", "description": "string(36)", "nullable": true, "example": "string"}}, "required": ["id", "client_id", "provider", "version", "external_provider_id", "name", "slug", "internal_id", "external_id", "enabled", "suspended", "meta", "image", "is_mobile", "is_bonus_ready", "is_wager_ready", "is_desktop", "has_lobby", "has_freespins", "genre_id", "friendly_url"]}, "SlotsProvider": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "client_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 2}, "subscription_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "name": {"type": "string", "description": "string(255)", "nullable": false, "example": "Amatic"}, "custom_name": {"type": "string", "description": "string(255)", "nullable": true, "example": "Name123"}, "image": {"type": "string", "description": "string(255)", "nullable": true, "example": "img.jpg"}, "suspended": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": 0}, "is_bonus_barred": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": 0}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2021-05-05T17:08:56.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2021-05-05T17:08:56.000000Z"}, "uuid": {"type": "string", "description": "string(36)", "nullable": true, "example": "string"}, "getProviders": {"type": "array", "items": {"$ref": "#/components/schemas/Slot"}}}, "required": ["id", "client_id", "subscription_id", "name", "suspended", "is_bonus_barred"]}, "User": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "client_id": {"type": "integer", "description": "integer", "nullable": false, "default": 2, "example": 2}, "login": {"type": "string", "description": "string(255)", "nullable": false, "example": "login"}, "email": {"type": "string", "description": "string(255)", "nullable": false, "example": "<EMAIL>"}, "role_id": {"type": "integer", "description": "integer", "nullable": true, "example": 1}, "last_seen_ip": {"type": "string", "description": "string(45)", "nullable": true, "example": "**********"}, "created_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2021-01-26T11:32:27.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": "2024-05-21T11:25:58.000000Z"}, "deleted_at": {"type": "string", "format": "date-time", "description": "datetime", "nullable": true, "example": null}}, "required": ["id", "client_id", "login", "email"]}, "UserBalance": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1384051329}, "player_id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1329956488}, "currency": {"type": "string", "description": "string(3)", "nullable": false, "example": "string"}, "last_transaction_id": {"type": "integer", "description": "integer", "nullable": true, "example": 1658121418}, "balance": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1048718542}, "debt": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1457642376}, "deposit": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1842657823}, "wager": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1622664836}, "withdraw": {"type": "integer", "format": "int64", "description": "bigint", "default": "99999999", "nullable": false, "example": 1723529057}, "in_game": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1266136333}, "current_payout_limit": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1665989831}, "total_bet": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1533521980}, "total_profit": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1902205755}, "payout_approved": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1099554656}, "payout_wait": {"type": "integer", "format": "int64", "description": "bigint", "default": "0", "nullable": false, "example": 1442534231}}, "required": ["id", "player_id", "currency", "balance", "debt", "deposit", "wager", "withdraw", "in_game", "current_payout_limit", "total_bet", "total_profit", "payout_approved", "payout_wait"]}, "Template": {"type": "object", "properties": {"__version__": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "id": {"type": "string", "description": "string(255)", "nullable": false, "example": "2393044457441333248"}, "name": {"type": "string", "description": "text", "nullable": true, "example": "test_name"}, "is_active": {"type": "boolean", "description": "boolean", "default": "1", "nullable": false, "example": true}, "max_bonus_number": {"type": "integer", "description": "integer", "nullable": false, "example": 1}, "type": {"type": "string", "description": "string(255)", "nullable": false, "example": "freebet"}, "operator_id": {"type": "string", "description": "string(255)", "nullable": false, "example": "1970602745463447552"}, "brand_id": {"type": "string", "description": "string(255)", "nullable": false, "example": "1970602745463447552"}, "event_scheduled": {"type": "string", "description": "datetime", "nullable": true, "example": "2024-07-18T11:29:02.000000Z"}, "from_time": {"type": "number", "description": "datetime", "nullable": true, "example": 1711976040}, "to_time": {"type": "number", "description": "datetime", "nullable": true, "example": 1735649640}, "days_to_use": {"type": "number", "nullable": false, "example": 100}, "events_availability": {"type": "string", "description": "text", "nullable": true, "example": "test"}, "restrictions": {"type": "string", "description": "text", "nullable": true, "example": "test"}, "freebet_data": {"type": "object", "properties": {"__version__": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}, "is_api_amount": {"type": "boolean", "description": "boolean", "default": "0", "nullable": false, "example": true}, "amount_list": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "type": {"type": "string", "description": "string(255)", "nullable": false, "example": "bet_refund"}, "min_selection": {"type": "integer", "description": "integer", "nullable": false, "example": 1}, "max_selection": {"type": "integer", "description": "integer", "nullable": false, "example": 1}, "min_odd": {"type": "integer", "nullable": false, "example": 1}, "max_odd": {"type": "integer", "nullable": false, "example": 1}, "bound_refund": {"type": "boolean", "nullable": false, "example": true}, "description": {"$ref": "#/components/schemas/SimpleObject"}, "reserve_template": {"type": "string", "description": "string(255)", "nullable": true}, "bet_restrictions": {"type": "object", "properties": {"type": {"type": "string", "description": "string(255)", "nullable": false, "example": "all"}, "bets_data": {"type": "array", "example": []}}}}}}}, "SimpleObject": {"type": "object", "example": {"key1": "value1", "key2": "value2"}}, "ArrayOfInt": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "ArrayOfString": {"type": "array", "items": {"type": "string"}, "example": ["string1", "string2"]}, "ArrayOfUserObjects": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "bigint", "nullable": false, "example": 1}}}}, "PaginationMeta": {"type": "object", "properties": {"current_page": {"type": "integer", "description": "Current page", "default": 1}, "from": {"type": "integer", "description": "From", "default": 1}, "last_page": {"type": "integer", "description": "Last page", "default": 11}, "links": {"$ref": "#/components/schemas/PaginationLinks"}, "path": {"type": "string", "description": "Path", "default": "http://localhost:8000/api/bonuses"}, "per_page": {"type": "integer", "description": "Items per page", "default": 15}, "to": {"type": "integer", "description": "Total pages", "example": 15}, "total": {"type": "integer", "description": "Total items", "example": 150}}}, "PaginationLinks": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "description": "URL", "example": "http://localhost:8000/api/bonuses?page=1"}, "label": {"type": "number", "description": "Label", "example": 1}, "active": {"type": "boolean", "description": "Active", "example": true}}}}, "PaginationFirstAndLastLinks": {"type": "object", "properties": {"first": {"type": "string", "description": "First", "example": "http://localhost:8000/api/bonuses?pagination%5Bpage%5D=1"}, "last": {"type": "string", "description": "Last", "example": "http://localhost:8000/api/bonuses?pagination%5Bpage%5D=11"}, "prev": {"type": "string", "description": "Previous", "nullable": true, "example": "http://localhost:8000/api/bonuses?pagination%5Bpage%5D=1"}, "next": {"type": "string", "description": "Next", "example": "http://localhost:8000/api/bonuses?pagination%5Bpage%5D=2"}}}}, "securitySchemes": {"bearerAuth": {"type": "https", "scheme": "bearer", "bearerFormat": "JWT"}}, "parameters": {"pagination": {"name": "pagination", "in": "query", "description": "Pagination", "required": false, "schema": {"type": "object", "properties": {"pagination": {"type": "array", "items": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number", "default": 1, "minLength": 1}, "limit": {"type": "integer", "description": "Items per page", "default": 10, "minLength": 1}}}}, "asc": {"type": "string", "description": "Sort by ascending", "nullable": true}, "desc": {"type": "string", "description": "Sort by descending", "nullable": true}}}}}}}