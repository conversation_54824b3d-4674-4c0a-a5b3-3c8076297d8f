<?php

namespace Tests\Unit;

use App\Clients\AppClient;
use App\Exceptions\Exception;
use App\Models\Bonus;
use App\Models\PromoCode;
use App\Repositories\PromoCodeRepository;
use App\Services\BonusApplyService;
use App\Services\Dto\PlayerDto;
use App\Services\Dto\PromoCodeDTO;
use App\Services\Dto\PromoCodeSearchDTO;
use App\Services\PlayerService;
use App\Services\PromoCodeService;
use App\Services\SlotProviderService;
use App\Services\SlotService;
use DateTimeImmutable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class PromoCodeServiceTest extends TestCase
{
    private PromoCodeService $promoCodeService;

    private PromoCodeRepository $mockPromoCodeRepository;

    private PlayerService $mockPlayerService;

    private BonusApplyService $mockBonusApplyService;

    public function setUp(): void
    {
        parent::setUp();

        $this->mockPromoCodeRepository = Mockery::mock(PromoCodeRepository::class);
        $this->mockPlayerService = Mockery::mock(PlayerService::class);
        $this->mockBonusApplyService = Mockery::mock(BonusApplyService::class);
        $this->slotServiceMock = Mockery::mock(SlotService::class);
        $this->slotProviderServiceMock = Mockery::mock(SlotProviderService::class);
        $this->appClientMock = Mockery::mock(AppClient::class);

        $this->promoCodeService = new PromoCodeService($this->mockPromoCodeRepository,
            $this->mockBonusApplyService, $this->mockPlayerService,
            $this->slotServiceMock, $this->slotProviderServiceMock,
            $this->appClientMock
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @throws Exception
     */
    public function testCreateSuccess()
    {
        $dto = $this->preparedDto();

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();
        DB::shouldReceive('rollBack')->never();

        $mockPromoCode = Mockery::mock(PromoCode::class)->makePartial();
        $mockBelongsTo = Mockery::mock(BelongsTo::class);
        $mockPromoCodeService = Mockery::mock(PromoCodeService::class)->makePartial();

        $mockPromoCodeService->shouldReceive('fill')
            ->once()
            ->with($dto)
            ->andReturn($mockPromoCode);

        $mockPromoCode->shouldReceive('bonus')
            ->once()
            ->andReturn($mockBelongsTo);

        $mockBelongsTo->shouldReceive('update')
            ->once()
            ->with([
                'data' => ['promo_code' => $dto->code],
                'is_promo' => true,
            ]);

        $mockPromoCodeService->shouldReceive('hydrateSinglePromoCodeWithAdditionalData')
            ->once()
            ->with($mockPromoCode)
            ->andReturn($mockPromoCode);

        $actualPromoCode = $mockPromoCodeService->create($dto);

        $this->assertInstanceOf(PromoCode::class, $actualPromoCode);
    }

    public function testCreateFailure()
    {
        $dto = $this->preparedDto();

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->never();
        DB::shouldReceive('rollBack')->once();

        $mockPromoCode = Mockery::mock(PromoCode::class)->makePartial();
        $mockBelongsTo = Mockery::mock(BelongsTo::class);
        $mockPromoCodeService = Mockery::mock(PromoCodeService::class)->makePartial();

        $mockPromoCodeService->shouldReceive('fill')
            ->once()
            ->with($dto)
            ->andReturn($mockPromoCode);

        $mockPromoCode->shouldReceive('bonus')
            ->once()
            ->andReturn($mockBelongsTo);

        $mockBelongsTo->shouldReceive('update')
            ->once()
            ->with([
                'data' => ['promo_code' => $dto->code],
                'is_promo' => true,
            ])
            ->andThrow(new Exception('Update failed'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Update failed');

        $actualPromoCode = $mockPromoCodeService->create($dto);

        $this->assertInstanceOf(PromoCode::class, $actualPromoCode);
    }

    public function testUpdate()
    {
        $promoCodeId = 1;
        $dto = new PromoCodeDTO([
            'id' => $promoCodeId,
            'name' => 'Updated Promo',
            'code' => 'TESTCODE',
            'client_id' => 2,
            'stream_id' => 2,
            'bonus_id' => 2434,
            'description' => 'Updated description',
            'active' => 1,
            'limit' => 1,
            'start_at' => 1721317160,
            'end_at' => null,
            'condition' => ['en' => 'updated_value'],
            'is_alanbase' => 1
        ]);

        $mockPromoCode = Mockery::mock(PromoCode::class)->makePartial();
        $mockPromoCodeService = Mockery::mock(PromoCodeService::class)->makePartial();
        $mockPromoCodeService->shouldReceive('fill')
            ->once()
            ->with($dto)
            ->andReturn($mockPromoCode);

        $mockPromoCodeService->shouldReceive('hydrateSinglePromoCodeWithAdditionalData')
            ->once()
            ->with($mockPromoCode)
            ->andReturn($mockPromoCode);

        $actualPromoCode = $mockPromoCodeService->update($dto);

        $this->assertInstanceOf(PromoCode::class, $actualPromoCode);
    }

    public function testList()
    {
        $dto = new PromoCodeSearchDTO([
            'name' => 'Test Promo',
            'code' => 'TESTCODE',
            'stream_id' => 1,
            'bonus_id' => 2434,
            'active' => 1,
            'limit' => 1,
            'page' => 1
        ]);

        $paginator = new LengthAwarePaginator([], 0, 10, 1);

        $this->mockPromoCodeRepository->shouldReceive('list')
            ->once()
            ->with($dto)
            ->andReturn($paginator);

        $this->slotServiceMock->shouldReceive('getSlotListByIds')->atLeast()->once()->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')->atLeast()->once()
            ->andReturn([]);

        $result = $this->promoCodeService->list($dto);

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertEquals($paginator, $result);
    }

    public function testGetPromoCode()
    {
        $code = 'TESTCODE';
        $mockPromoCodeService = Mockery::mock(PromoCodeService::class, [
            $this->mockPromoCodeRepository,
            $this->mockBonusApplyService,
            $this->mockPlayerService,
            $this->slotServiceMock,
            $this->slotProviderServiceMock,
            $this->appClientMock
        ])->makePartial();

        $mockPromoCode = Mockery::mock(PromoCode::class)->makePartial();
        $this->mockPromoCodeRepository->shouldReceive('getPromoCode')
            ->once()
            ->with($code)
            ->andReturn($mockPromoCode);

        $mockPromoCodeService->shouldReceive('hydrateSinglePromoCodeWithAdditionalData')
            ->once()
            ->with($mockPromoCode)
            ->andReturn($mockPromoCode);

        $result = $mockPromoCodeService->getPromoCode($code);

        $this->assertInstanceOf(PromoCode::class, $result);
    }

    /**
     * @throws Exception
     */
    public function testApplySuccess()
    {
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();
        DB::shouldReceive('rollBack')->never();

        $playerId = 123;
        $code = 'PROMO123';

        $promoCode = Mockery::mock(PromoCode::class)->makePartial();
        $bonus = Mockery::mock(Bonus::class)->makePartial();
        $player = Mockery::mock(PlayerDto::class);

        $bonus->shouldReceive('getAttribute')->with('is_onetime_group')->andReturn(true);

        $promoCode->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $promoCode->shouldReceive('getAttribute')->with('code')->andReturn($code);
        $promoCode->shouldReceive('getAttribute')->with('uses')->andReturn(0);
        $promoCode->shouldReceive('getAttribute')->with('bonus')->andReturn($bonus);

        $this->mockPromoCodeRepository->shouldReceive('getPromoCode')
            ->with($code)
            ->andReturn($promoCode);

        $this->mockPlayerService->shouldReceive('getPlayerById')
            ->with($playerId)
            ->andReturn($player);

        $this->mockPromoCodeRepository->shouldReceive('attachPlayer')
            ->with($promoCode->id, $playerId)
            ->once();

        $this->mockBonusApplyService->shouldReceive('applyOnetimeForPlayer')
            ->with($promoCode->bonus, $player)
            ->once();

        $promoCode->shouldReceive('setAttribute')->with('uses', 1)->once();

        $this->mockPromoCodeRepository->shouldReceive('save')
            ->with($promoCode)
            ->once()
            ->andReturn(true);

        $this->slotServiceMock->shouldReceive('getSlotListByIds')->atLeast()->once()->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')->atLeast()->once()
            ->andReturn([]);

        $this->appClientMock->shouldReceive('emitApplyPromoCodeEvents')->once()->andReturn(['status' => 'success']);

        $result = $this->promoCodeService->apply($code, $playerId);

        $this->assertSame($promoCode, $result);
    }

    /**
     * @throws Exception
     */
    public function testApplyThrowsExceptionAndRollsBack()
    {
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->never();
        DB::shouldReceive('rollBack')->once();

        $promoCode = Mockery::mock(PromoCode::class);
        $bonus = Mockery::mock(Bonus::class);
        $player = Mockery::mock(PlayerDto::class);

        $bonus->shouldReceive('getAttribute')->with('is_welcome_group')->andReturn(false);
        $bonus->shouldReceive('getAttribute')->with('is_onetime_group')->andReturn(true);
        $bonus->shouldReceive('getAttribute')->with('is_no_dep')->andReturn(false);

        $promoCode->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $promoCode->shouldReceive('getAttribute')->with('uses')->andReturn(0);
        $promoCode->shouldReceive('getAttribute')->with('bonus')->andReturn($bonus);

        $playerId = 123;
        $code = 'PROMO123';

        $this->mockPromoCodeRepository->shouldReceive('getPromoCode')
            ->with($code)
            ->andReturn($promoCode);

        $this->mockPlayerService->shouldReceive('getPlayerById')
            ->with($playerId)
            ->andReturn($player);

        $this->mockPromoCodeRepository->shouldReceive('attachPlayer')
            ->with($promoCode->id, $playerId)
            ->andThrow(new Exception('Test exception'));

        $this->expectException(Exception::class);
        $this->promoCodeService->apply($code, $playerId);
    }

    private function preparedDto(): PromoCodeDTO
    {
        return new PromoCodeDTO([
            'name' => 'test name',
            'code' => 'TESTCODE',
            'client_id' => 2,
            'bonus_id' => 2434,
            'start_at' => 1721317160,
            'end_at' => null,
            'condition' => ['en' => 'value'],
            'is_alanbase' => 1,
        ]);
    }
}
