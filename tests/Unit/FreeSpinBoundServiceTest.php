<?php

namespace Tests\Unit;

use App\Enums\SupplierEnum;
use Exception;
use App\Models\FreeSpin;
use App\Models\FreeSpinBound;
use App\Repositories\FreeSpinBoundRepository;
use App\Services\BonusBalanceService;
use App\Services\Dto\FreeSpinBoundSearchDTO;
use App\Services\FreeSpinBoundService;
use App\Services\FreeSpinService;
use App\Services\GamicorpFreeSpinBoundService;
use App\Services\PlayerService;
use App\Services\SendToSlotegratorHttpService;
use App\Services\SlotegratorFreeSpinBoundService;
use App\Services\SlotService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Mockery;
use Tests\TestCase;

class FreeSpinBoundServiceTest extends TestCase
{
    private FreeSpinService $mockFreeSpinService;
    private FreeSpinBoundRepository $mockFreeSpinBoundRepository;
    private PlayerService $mockPlayerService;
    private FreeSpinBoundService $freeSpinBoundService;
    private SlotService $mockSlotService;
    private GamicorpFreeSpinBoundService $mockGamicorpFreeSpinBoundService;
    private BonusBalanceService $mockBonusBalanceService;
    private SendToSlotegratorHttpService $mockSendToSlotegratorHttpService;
    private SlotegratorFreeSpinBoundService $mockSlotegratorFreeSpinBoundService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockFreeSpinBoundRepository = Mockery::mock(FreeSpinBoundRepository::class);
        $this->mockPlayerService = Mockery::mock(PlayerService::class);
        $this->mockFreeSpinBoundService = Mockery::mock(FreeSpinBoundService::class);
        $this->mockSlotService = Mockery::mock(SlotService::class);
        $this->mockGamicorpFreeSpinBoundService = Mockery::mock(GamicorpFreeSpinBoundService::class);
        $this->mockBonusBalanceService = Mockery::mock(BonusBalanceService::class);
        $this->mockSendToSlotegratorHttpService = Mockery::mock(SendToSlotegratorHttpService::class);
        $this->mockSlotegratorFreeSpinBoundService = Mockery::mock(SlotegratorFreeSpinBoundService::class);
        $this->mockFreeSpinService = Mockery::mock(FreeSpinService::class);

        $this->freeSpinBoundService = new FreeSpinBoundService(
            $this->mockFreeSpinBoundRepository,
            $this->mockBonusBalanceService,
            $this->mockSendToSlotegratorHttpService,
            $this->mockSlotService,
            $this->mockGamicorpFreeSpinBoundService,
            $this->mockPlayerService,
            $this->mockSlotegratorFreeSpinBoundService
        );
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testGetPlayerFreeSpinsByActiveStatus()
    {
        $expectedActiveFreeSpins = new Collection([new FreeSpinBound()]);
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getPlayerActiveFreeSpinBounds')->once()
            ->with(1)
            ->andReturn($expectedActiveFreeSpins);

        $this->mockSlotService
            ->shouldReceive('getSlotListByIds')
            ->andReturn([]);

        $result = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus(1, 'active');

        $this->assertEquals($expectedActiveFreeSpins, $result);
    }

    public function testGetPlayerFreeSpinsByArchivedStatus()
    {
        $expectedArchivedFreeSpins = new Collection(new FreeSpinBound());
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getPlayerArchivedFreeSpinBounds')->once()
            ->with(1)
            ->andReturn($expectedArchivedFreeSpins);

        $this->mockSlotService
            ->shouldReceive('getSlotListByIds')
            ->andReturn([]);

        $result = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus(1, 'archived');

        $this->assertEquals($expectedArchivedFreeSpins, $result);
    }

    public function testUpdate()
    {
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('updateFreeSpinBounds')
            ->with([1], [1])
            ->once()
            ->andReturn(1);

        $result = $this->freeSpinBoundService->update([1], [1]);
        $this->assertEquals(1, $result);
    }

    public function testGetPlayersByFreeSpinID()
    {
        $dto = new FreeSpinBoundSearchDTO([
            'id' => 1
        ]);

        $mockLengthAwarePaginator = new LengthAwarePaginator([], 0, 10);

        $mockFreeSpinService = Mockery::mock(FreeSpinBoundService::class)
            ->shouldAllowMockingProtectedMethods()
            ->makePartial();

        $mockFreeSpinService->shouldReceive('search')
            ->once()
            ->with($dto)
            ->andReturn($mockLengthAwarePaginator);

        $mockFreeSpinService->shouldReceive('hydrateFreeSpinsBoundsWithPlayers')
            ->once()
            ->with($mockLengthAwarePaginator);

        $result = $mockFreeSpinService->getFreeSpinBoundList($dto);

        $this->assertSame($mockLengthAwarePaginator, $result);
    }

    /**
     * @throws Exception
     */
    public function testAttachFreeSpinToPlayer_Gamicorp()
    {
        $bonusId = 1;
        $playerId = 100;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', SupplierEnum::GAMICORP_INTEGRATION->value);

        $this->mockFreeSpinService->shouldReceive('getFreeSpinByBonusId')
            ->once()
            ->with($bonusId)
            ->andReturn($freeSpin);

        $this->mock(FreeSpinService::class, function ($mock) {
            $mock->shouldReceive('getFreeSpinByBonusId')
                ->andReturnUsing(fn ($bonusId) => $this->mockFreeSpinService->getFreeSpinByBonusId($bonusId));
        });

        $this->mockGamicorpFreeSpinBoundService->shouldReceive('attach')
            ->once()
            ->with($freeSpin, $playerId)
            ->andReturn(true);

        $result = $this->freeSpinBoundService->attachFreeSpinToPlayer($bonusId, $playerId);

        $this->assertTrue($result);
    }

    /**
     * @throws Exception
     */
    public function testAttachFreeSpinToPlayer_Slotegrator()
    {
        $bonusId = 2;
        $playerId = 200;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', SupplierEnum::SLOTEGRATOR->value);

        $this->mockFreeSpinService->shouldReceive('getFreeSpinByBonusId')
            ->once()
            ->with($bonusId)
            ->andReturn($freeSpin);

        $this->mock(FreeSpinService::class, function ($mock) {
            $mock->shouldReceive('getFreeSpinByBonusId')
                ->andReturnUsing(fn ($bonusId) => $this->mockFreeSpinService->getFreeSpinByBonusId($bonusId));
        });

        $this->mockSlotegratorFreeSpinBoundService->shouldReceive('attach')
            ->once()
            ->with($freeSpin, $playerId)
            ->andReturn(true);

        $result = $this->freeSpinBoundService->attachFreeSpinToPlayer($bonusId, $playerId);

        $this->assertTrue($result);
    }

    /**
     * @throws Exception
     */
    public function testAttachFreeSpinToPlayer_UnknownSupplier()
    {
        $bonusId = 3;
        $playerId = 300;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', 'Unknown supplier');

        $this->mockFreeSpinService->shouldReceive('getFreeSpinByBonusId')
            ->once()
            ->with($bonusId)
            ->andReturn($freeSpin);

        $this->mock(FreeSpinService::class, function ($mock) {
            $mock->shouldReceive('getFreeSpinByBonusId')
                ->andReturnUsing(fn ($bonusId) => $this->mockFreeSpinService->getFreeSpinByBonusId($bonusId));
        });

        $result = $this->freeSpinBoundService->attachFreeSpinToPlayer($bonusId, $playerId);

        $this->assertFalse($result);
    }

    /**
     * @throws Exception
     */
    public function testCancelFreeSpinBound_Gamicorp()
    {
        $freeSpinBoundId = 1;
        $playerId = 100;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', SupplierEnum::GAMICORP_INTEGRATION->value);

        $freeSpinBound = new FreeSpinBound();
        $freeSpinBound->setAttribute('id', $freeSpinBoundId);
        $freeSpinBound->setAttribute('player_id', $playerId);

        $this->mockFreeSpinBoundRepository->shouldReceive('getFreeSpinDetailByBoundId')
            ->once()
            ->with($freeSpinBoundId)
            ->andReturn($freeSpin);

        $this->mockFreeSpinBoundRepository->shouldReceive('getFreeSpinBoundDetailById')
            ->once()
            ->with($freeSpinBoundId)
            ->andReturn($freeSpinBound);

        $this->mockGamicorpFreeSpinBoundService->shouldReceive('cancelFreespinBound')
            ->once()
            ->with($freeSpinBound)
            ->andReturn(true);

        $result = $this->freeSpinBoundService->cancelFreeSpinBound($freeSpinBoundId, $playerId);

        $this->assertTrue($result);
    }

    /**
     * @throws \App\Exceptions\Exception
     */
    public function testCancelFreeSpinBound_ThrowsUnauthorizedException()
    {
        $freeSpinBoundId = 2;
        $playerId = 100;
        $wrongPlayerId = 200;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', SupplierEnum::GAMICORP_INTEGRATION->value);

        $freeSpinBound = new FreeSpinBound();
        $freeSpinBound->setAttribute('id', $freeSpinBoundId);
        $freeSpinBound->setAttribute('player_id', $wrongPlayerId);

        $this->mockFreeSpinBoundRepository->shouldReceive('getFreeSpinDetailByBoundId')
            ->once()
            ->with($freeSpinBoundId)
            ->andReturn($freeSpin);

        $this->mockFreeSpinBoundRepository->shouldReceive('getFreeSpinBoundDetailById')
            ->once()
            ->with($freeSpinBoundId)
            ->andReturn($freeSpinBound);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Unauthorized, please pass a valid core bearer token');

        $this->freeSpinBoundService->cancelFreeSpinBound($freeSpinBoundId, $playerId);
    }
}
