<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Enums\AggregatorEnum;
use App\Enums\SupplierEnum;
use App\Events\FreeSpinResendEvent;
use App\Exceptions\Exception;
use App\Jobs\FreeSpins\FreeSpinsUpdateJob;
use App\Models\FreeSpin;
use App\Models\FreeSpinBound;
use App\Repositories\FreeSpinBoundRepository;
use App\Repositories\FreeSpinRepository;
use App\Services\BonusService;
use App\Services\Dto\FreeSpinCrudDto;
use App\Services\Dto\FreeSpinSearchDto;
use App\Services\Dto\PlayerDto;
use App\Services\FreeSpinBoundService;
use App\Services\FreeSpinService;
use App\Services\PlayerService;
use App\Services\SlotService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Exception as BaseException;
use Mockery\MockInterface;
use Throwable;
use Mockery;
use Tests\CreatesApplication;

class FreeSpinServiceTest extends TestCase
{
    use CreatesApplication;
    use WithFaker;

    private FreeSpinService $freeSpinService;
    private FreeSpinRepository|MockInterface $mockFreeSpinRepository;
    private FreeSpinBoundRepository|MockInterface $mockFreeSpinBoundRepository;
    private MockInterface|PlayerService $mockPlayerService;
    private FreeSpinBoundService|MockInterface $mockFreeSpinBoundService;
    private MockInterface|SlotService $mockSlotService;
    private FreeSpinService|MockInterface $mockFreeSpinService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockFreeSpinRepository = Mockery::mock(FreeSpinRepository::class);
        $this->mockFreeSpinBoundRepository = Mockery::mock(FreeSpinBoundRepository::class);
        $mockBonusService = Mockery::mock(BonusService::class);
        $this->mockPlayerService = Mockery::mock(PlayerService::class);
        $this->mockFreeSpinBoundService = Mockery::mock(FreeSpinBoundService::class);
        $this->mockSlotService = Mockery::mock(SlotService::class);

        $this->freeSpinService = new FreeSpinService(
            $this->mockFreeSpinRepository,
            $this->mockFreeSpinBoundRepository,
            $mockBonusService,
            $this->mockFreeSpinBoundService,
            $this->mockPlayerService,
            $this->mockSlotService,
        );

        $this->mockFreeSpinService = Mockery::mock(FreeSpinService::class, [
            $this->mockFreeSpinRepository,
            $this->mockFreeSpinBoundRepository,
            $mockBonusService,
            $this->mockFreeSpinBoundService,
            $this->mockPlayerService,
            $this->mockSlotService,
        ])->makePartial();

        DB::shouldReceive('transaction')->andReturnUsing(static fn($callback) => $callback());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testGetFreeSpinByBonusId(): void
    {
        $freespin = new FreeSpin();
        $bonusId = 1;
        $this->mockFreeSpinRepository->shouldReceive('getFreeSpinDetailByBonusId')->once()
            ->with($bonusId)
            ->andReturn($freespin);

        $result = $this->freeSpinService->getFreeSpinByBonusId($bonusId);

        $this->assertSame($freespin, $result);
    }

    public function testListMethod(): void
    {
        $freeSpinSearchDto = new FreeSpinSearchDto([]);

        $paginator = new LengthAwarePaginator([], 0, 10);
        $this->mockFreeSpinRepository->shouldReceive('getWithPagination')->once()
            ->with($freeSpinSearchDto)
            ->andReturn($paginator);

        $this->mockSlotService->shouldReceive('getSlotListByIds')->once();

        $result = $this->freeSpinService->list($freeSpinSearchDto);

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    public function testGetFreeSpinByIdMethod(): void
    {
        $freeSpinId = 1;
        $mockFreeSpin = new FreeSpin();
        $mockFreeSpin->id = $freeSpinId;
        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')->once()
            ->with($freeSpinId)
            ->andReturn($mockFreeSpin);

        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getLimitedPlayersByFreeSpinId')->once()
            ->with($freeSpinId)
            ->andReturn(Collection::empty());

        $this->mockSlotService->shouldReceive('getSlotListByIds')
            ->once()->andReturn([]);
        $this->mockPlayerService->shouldReceive('getPlayersByIds')
            ->once()->andReturn(\Illuminate\Support\Collection::empty());

        $this->mockFreeSpinBoundRepository->shouldReceive('getLimitedPlayersByFreeSpinId')
            ->andReturn(new Collection());

        $result = $this->freeSpinService->getFreeSpinById($freeSpinId);

        $this->assertSame($freeSpinId, $result->id);
    }

    public function testGetFreeSpinByIdMethodThrowsModelNotFoundException(): void
    {
        $freeSpin = new FreeSpin();
        $freeSpin->id = 999999999999;
        $freeSpin->setAttribute('bounds', new Collection([]));

        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')->once()
            ->with($freeSpin->id)
            ->andReturn($freeSpin);

        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getLimitedPlayersByFreeSpinId')->once()
            ->with($freeSpin->id)
            ->andReturn(Collection::empty());

        $this->mockSlotService->shouldReceive('getSlotListByIds')
            ->once()->andReturn([]);

        $this->mockPlayerService->shouldReceive('getPlayersByIds')
            ->once()->andReturn(\Illuminate\Support\Collection::empty());

        $this->mockFreeSpinBoundRepository->shouldReceive('getLimitedPlayersByFreeSpinId')
            ->andReturn(Collection::empty());

        $result = $this->freeSpinService->getFreeSpinById($freeSpin->id);
        $this->assertSame($freeSpin->id, $result->id);
    }

    public function testGetPlayersArray(): void
    {
        $players = [
            ['uuid' => 'player1'],
            ['uuid' => 'player2'],
            ['uuid' => 'player1'],
        ];

        $playersFromDB = new Collection([
            ['id' => 1],
            ['id' => 2],
        ]);

        $this->mockPlayerService
            ->shouldReceive('getPlayersByUUIDs')->once()
            ->with(['player1', 'player2'])
            ->andReturn($playersFromDB);

        $result = $this->freeSpinService->getPlayersArray($players);

        $this->assertSame([1, 2], $result);
    }

    public function testSaveFreeSpinNew(): void
    {
        $data = new FreeSpinCrudDto([
            'id' => null,
            'slots_id' => 1,
            'provider_id' => 1,
            'supplier' => SupplierEnum::GAMICORP_INTEGRATION->value,
            'aggregator' => AggregatorEnum::SOFTSWISS->value,
            'title' => 'Test title',
            'name' => 'Test test',
            'count' => 1,
            'bet' => 1,
            'currency' => 'EUR',
            'bet_id' => 1,
            'author_id' => 1,
            'denomination' => 1.0,
            'start_at' => Carbon::now()->addDay()->toDateTimeString(),
            'expired_at' => Carbon::now()->addMonth()->toDateTimeString(),
            'bonus_balance' => false,
            'wager' => 1.0,
            'status' => '',
            'is_active' => true,
        ]);
        $type = FreeSpin::MANUAL_TYPE;
        $bonusId = 123;

        $this->mockFreeSpinRepository
            ->shouldReceive('save')->once()
            ->andReturn(true);

        $result = $this->freeSpinService->saveFreeSpin($data, $type, $bonusId);
        $this->assertNull($result->id);
    }

    public function testChangeFreeSpinProcessStatus(): void
    {
        $this->mockFreeSpinRepository->shouldReceive('changeFreeSpinProcessStatus')
            ->once()->andReturn(null);

        $this->freeSpinService->changeFreeSpinProcessStatus(1, 1);
    }

    public function testCheckFreeSpinName(): void
    {
        $this->mockFreeSpinRepository->shouldReceive('checkFreeSpinName')->with('test', 1)
            ->once()->andReturn(false);
        $result = $this->freeSpinService->checkFreeSpinName('test', 1);
        $this->assertArrayHasKey('exists', $result);
        $this->assertFalse($result['exists']);
    }

    public function testRemoveFreeSpin(): void
    {
        Event::fake();
        $freespin = new FreeSpin();
        $freespin->id = 1;

        $this->mockFreeSpinRepository->shouldReceive('getFreeSpinDetailById')
            ->once()->andReturn($freespin);
        $this->mockFreeSpinRepository->shouldReceive('remove')
            ->once()->andReturn(null);

        $this->freeSpinService->removeFreeSpin($freespin->id);
    }

    /**
     * @throws Exception
     */
    public function testResendFreeSpinWithBoundId(): void
    {
        $freespinId = 1;
        $freespinBoundId = 10;

        $freespin = new FreeSpin();
        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')->once()
            ->with($freespinId)
            ->andReturn($freespin);

        $freespinBound = new FreeSpinBound();
        $freespinBound->is_active = false;
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getFreeSpinBoundDetailById')->once()
            ->with($freespinBoundId)
            ->andReturn($freespinBound);

        $carbon = Carbon::now();
        Carbon::setTestNow($carbon);

        $this->mockFreeSpinBoundService
            ->shouldReceive('update')->once()
            ->with(['start_at' => Carbon::now()->addMinute()], [$freespinBoundId]);

        Event::fake([
            FreeSpinResendEvent::class,
        ]);

        Event::assertNotDispatched(FreeSpinResendEvent::class);

        $this->freeSpinService->resendFreeSpin($freespinId, $freespinBoundId);
    }

    /**
     * @throws Exception
     */
    public function testResendFreeSpinWithoutBoundId(): void
    {
        $freespinId = 1;
        $freespinBoundId = null;

        $freespin = new FreeSpin();
        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')->once()
            ->with($freespinId)
            ->andReturn($freespin);

        $freespinBoundIdsArray = [2, 3];
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getAllNotSentBoundsByFreespinId')->once()
            ->with($freespinId)
            ->andReturn($freespinBoundIdsArray);

        $carbon = Carbon::now();
        Carbon::setTestNow($carbon);

        $this->mockFreeSpinBoundService
            ->shouldReceive('update')
            ->with(['start_at' => Carbon::now()->addMinute()], $freespinBoundIdsArray);

        Event::fake([
            FreeSpinResendEvent::class,
        ]);

        $this->freeSpinService->resendFreeSpin($freespinId, $freespinBoundId);
    }

    public function testResendFreeSpinException(): void
    {
        $freespinId = 1;
        $freespinBoundId = 10;

        $freespin = new FreeSpin();
        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')->once()
            ->with($freespinId)
            ->andReturn($freespin);

        $freespinBound = new FreeSpinBound();
        $freespinBound->is_active = true;
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getFreeSpinBoundDetailById')->once()
            ->with($freespinBoundId)
            ->andReturn($freespinBound);

        $isError = false;

        try {
            $this->freeSpinService->resendFreeSpin($freespinId, $freespinBoundId);
        } catch (BaseException) {
            $isError = true;
        }
        $this->assertTrue($isError);
    }

    /**
     * @throws Throwable
     */
    public function testStore(): void
    {
        Event::fake();
        $dto = new FreeSpinCrudDto([
            'id' => 1,
            'slots_id' => 1,
            'provider_id' => 1,
            'supplier' => SupplierEnum::GAMICORP_INTEGRATION->value,
            'aggregator' => AggregatorEnum::SOFTSWISS->value,
            'title' => 'Test title',
            'name' => 'Test test',
            'count' => 1,
            'bet' => 1,
            'currency' => 'EUR',
            'bet_id' => 1,
            'author_id' => 1,
            'denomination' => 1.0,
            'start_at' => Carbon::now()->addDay()->toDateTimeString(),
            'expired_at' => Carbon::now()->addMonth()->toDateTimeString(),
            'players' => [['uuid' => 'test_uuid']],
            'bonus_balance' => false,
            'wager' => 1.0,
            'status' => '',
            'is_active' => true,
        ]);

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $freeSpin = Mockery::mock(FreeSpin::class)->makePartial();
        $freeSpin->id = 1;
        $freeSpin->shouldReceive('load')->andReturnSelf();

        $this->mockPlayerService
            ->shouldReceive('getPlayersByUUIDs')
            ->andReturn(new Collection([$player]));

        $this->mockFreeSpinRepository
            ->shouldReceive('save')
            ->andReturn(true);

        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')
            ->with($dto->id)
            ->andReturn($freeSpin);

        $this->mockFreeSpinBoundService
            ->shouldReceive('attachFreeSpinToPlayers')
            ->with($freeSpin, [1]);

        $this->mockSlotService->shouldReceive('getSlotListByIds')
            ->atMost()->once()->andReturn([]);
        $this->mockPlayerService->shouldReceive('getPlayersByIds')
            ->atMost()->once()->andReturn(\Illuminate\Support\Collection::empty());

        $result = $this->mockFreeSpinService->store($dto);

        $this->assertSame($freeSpin->id, $result->id);
    }

    public function testUpdateFreeSpinException(): void
    {
        $dto = new FreeSpinCrudDto([
            'id' => 1,
            'slots_id' => 1,
            'provider_id' => 1,
            'supplier' => SupplierEnum::GAMICORP_INTEGRATION->value,
            'aggregator' => AggregatorEnum::SOFTSWISS->value,
            'title' => 'Test title',
            'name' => 'Test test',
            'count' => 1,
            'bet' => 1,
            'currency' => 'EUR',
            'bet_id' => 1,
            'author_id' => 1,
            'denomination' => 1.0,
            'start_at' => Carbon::now()->addDay()->toDateTimeString(),
            'expired_at' => Carbon::now()->addMonth()->toDateTimeString(),
            'players' => [['uuid' => 'test_uuid']],
            'bonus_balance' => false,
            'wager' => 1.0,
            'status' => '',
            'is_active' => true,
        ]);
        $freespin = new FreeSpin();
        $freespin->id = 1;
        $this->mockFreeSpinRepository
            ->shouldReceive('checkFreeSpinName')
            ->andReturn(true);
        $isError = false;

        try {
            $this->freeSpinService->updateFreeSpin($dto);
        } catch (BaseException) {
            $isError = true;
        }
        $this->assertTrue($isError);
    }

    public function testUpdateFreeSpinException2(): void
    {
        $dto = new FreeSpinCrudDto([
            'id' => 1,
            'slots_id' => 1,
            'provider_id' => 1,
            'supplier' => SupplierEnum::GAMICORP_INTEGRATION->value,
            'aggregator' => AggregatorEnum::SOFTSWISS->value,
            'title' => 'Test title',
            'name' => 'Test test',
            'count' => 1,
            'bet' => 1,
            'currency' => 'EUR',
            'bet_id' => 1,
            'author_id' => 1,
            'denomination' => 1.0,
            'start_at' => Carbon::now()->addDay()->toDateTimeString(),
            'expired_at' => Carbon::now()->addMonth()->toDateTimeString(),
            'players' => [['uuid' => 'test_uuid']],
            'bonus_balance' => false,
            'wager' => 1.0,
            'status' => '',
            'is_active' => true,
        ]);
        $freespin = new FreeSpin();
        $freespin->setAttribute('bounds', new Collection([]));
        $freespin->id = 1;

        $this->mockFreeSpinRepository
            ->shouldReceive('checkFreeSpinName')
            ->andReturn(false);
        $freespin->in_process = true;

        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')
            ->andReturn($freespin);

        $this->mockSlotService->shouldReceive('getSlotListByIds')
            ->atMost()->once()->andReturn([]);
        $this->mockPlayerService->shouldReceive('getPlayersByIds')
            ->atMost()->once()->andReturn(\Illuminate\Support\Collection::empty());


        $isError = false;

        try {
            $this->freeSpinService->updateFreeSpin($dto);
        } catch (BaseException) {
            $isError = true;
        }
        $this->assertTrue($isError);
    }

    public function testUpdateFreeSpinException3(): void
    {
        $dto = new FreeSpinCrudDto([
            'id' => 1,
            'slots_id' => 1,
            'provider_id' => 1,
            'supplier' => SupplierEnum::GAMICORP_INTEGRATION->value,
            'aggregator' => AggregatorEnum::SOFTSWISS->value,
            'title' => 'Test title',
            'name' => 'Test test',
            'count' => 1,
            'bet' => 1,
            'currency' => 'EUR',
            'bet_id' => 1,
            'author_id' => 1,
            'denomination' => 1.0,
            'start_at' => Carbon::now()->subDay()->toDateTimeString(),
            'expired_at' => Carbon::now()->addMonth()->toDateTimeString(),
            'players' => [['uuid' => 'test_uuid']],
            'bonus_balance' => false,
            'wager' => 1.0,
            'status' => '',
            'is_active' => true,
        ]);
        $freespin = new FreeSpin();
        $freespin->setAttribute('bounds', new Collection([]));
        $freespin->id = 1;

        $this->mockFreeSpinRepository
            ->shouldReceive('checkFreeSpinName')
            ->andReturn(false);
        $freespin->in_process = false;

        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')
            ->andReturn($freespin);

        $this->mockSlotService->shouldReceive('getSlotListByIds')
            ->atMost()->once()->andReturn([]);
        $this->mockPlayerService->shouldReceive('getPlayersByIds')
            ->atMost()->once()->andReturn(\Illuminate\Support\Collection::empty());

        $isError = false;

        try {
            $this->freeSpinService->updateFreeSpin($dto);
        } catch (BaseException) {
            $isError = true;
        }
        $this->assertTrue($isError);
    }

    /**
     * @throws Exception
     */
    public function testUpdateFreeSpin(): void
    {
        Bus::fake();
        $dto = new FreeSpinCrudDto([
            'id' => 1,
            'slots_id' => 1,
            'provider_id' => 1,
            'supplier' => SupplierEnum::GAMICORP_INTEGRATION->value,
            'aggregator' => AggregatorEnum::SOFTSWISS->value,
            'title' => 'Test title',
            'name' => 'Test test',
            'count' => 1,
            'bet' => 1,
            'currency' => 'EUR',
            'bet_id' => 1,
            'author_id' => 1,
            'denomination' => 1.0,
            'start_at' => Carbon::now()->addDay()->toDateTimeString(),
            'expired_at' => Carbon::now()->addMonth()->toDateTimeString(),
            'players' => [['uuid' => 'test_uuid']],
            'bonus_balance' => false,
            'wager' => 1.0,
            'status' => '',
            'is_active' => true,
        ]);
        $freespin = new FreeSpin();

        $freespin->setAttribute('bounds', new Collection([]));
        $freespin->id = 1;

        $this->mockFreeSpinRepository
            ->shouldReceive('checkFreeSpinName')
            ->andReturn(false);
        $freespin->in_process = false;
        $freespin->start_at = Carbon::now()->addDay();

        $this->mockFreeSpinRepository
            ->shouldReceive('getFreeSpinDetailById')
            ->andReturn($freespin);
        $this->mockFreeSpinRepository
            ->shouldReceive('changeFreeSpinProcessStatus');

        $this->mockSlotService->shouldReceive('getSlotListByIds')
            ->andReturn([]);
        $this->mockPlayerService->shouldReceive('getPlayersByIds')
            ->atMost()->once()->andReturn(\Illuminate\Support\Collection::empty());

        $freeSpin = $this->freeSpinService->updateFreeSpin($dto);

        Bus::assertDispatched(FreeSpinsUpdateJob::class);

        $this->assertSame($freespin->id, $freeSpin->id);
    }
}
