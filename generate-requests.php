<?php

declare(strict_types=1);

/**
 * <PERSON>ript to generate Laravel Form Request classes from OpenAPI generated Model classes
 */

// Configuration
$generatedModelsPath = 'generated-code/Model';
$outputPath = 'generated-code/Http/Requests';
$namespace = 'App\\Http\\Requests';
$baseRequestClass = 'App\\Http\\Requests\\BaseRequest';

// Request models that should be converted to Form Requests
$requestModels = [
    'CreateBonusRequest' => 'CreateBonusRequest',
    'UpdateBonusRequest' => 'UpdateBonusRequest',
];

// Create output directory if it doesn't exist
if (!is_dir($outputPath)) {
    mkdir($outputPath, 0755, true);
}

foreach ($requestModels as $modelName => $requestName) {
    $modelFile = $generatedModelsPath . '/' . $modelName . '.php';
    
    if (!file_exists($modelFile)) {
        echo "Model file not found: $modelFile\n";
        continue;
    }
    
    $modelContent = file_get_contents($modelFile);
    
    // Extract properties from the model constructor
    $properties = extractProperties($modelContent);
    
    // Generate Form Request class
    $requestContent = generateFormRequest($requestName, $namespace, $baseRequestClass, $properties);
    
    // Write to file
    $outputFile = $outputPath . '/' . $requestName . '.php';
    file_put_contents($outputFile, $requestContent);
    
    echo "Generated: $outputFile\n";
}

function extractProperties(string $modelContent): array
{
    $properties = [];
    
    // Extract constructor parameters
    if (preg_match('/public function __construct\((.*?)\)/s', $modelContent, $matches)) {
        $constructorParams = $matches[1];
        
        // Parse parameters
        $lines = explode("\n", $constructorParams);
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || $line === ',') continue;
            
            // Match parameter pattern: public ?type $name = null,
            if (preg_match('/public\s+(\??)([\w\\\\|]+)\s+\$(\w+)(\s*=\s*[^,]+)?,?/', $line, $paramMatches)) {
                $isNullable = !empty($paramMatches[1]);
                $type = $paramMatches[2];
                $name = $paramMatches[3];
                $hasDefault = !empty($paramMatches[4]);
                
                $properties[] = [
                    'name' => $name,
                    'type' => $type,
                    'nullable' => $isNullable || $hasDefault,
                    'required' => !$isNullable && !$hasDefault,
                ];
            }
        }
    }
    
    return $properties;
}

function generateFormRequest(string $className, string $namespace, string $baseClass, array $properties): string
{
    $phpDoc = generatePhpDoc($properties);
    $rules = generateValidationRules($properties);
    
    return <<<PHP
<?php

declare(strict_types=1);

namespace $namespace;

use $baseClass;
use Illuminate\Validation\Rule;

/**
 * Class $className
 *
$phpDoc
 */
class $className extends BaseRequest
{
    /**
     * @return array<string, array<string>>
     */
    public function rules(): array
    {
        return [
$rules
        ];
    }
}

PHP;
}

function generatePhpDoc(array $properties): string
{
    $phpDoc = '';
    foreach ($properties as $property) {
        $type = mapTypeToPhpDoc($property['type'], $property['nullable']);
        $phpDoc .= " * @property $type \${$property['name']}\n";
    }
    return rtrim($phpDoc);
}

function generateValidationRules(array $properties): string
{
    $rules = '';
    foreach ($properties as $property) {
        $validationRules = mapTypeToValidationRules($property['type'], $property['required']);
        $rules .= "            '{$property['name']}' => [$validationRules],\n";
    }
    return rtrim($rules);
}

function mapTypeToPhpDoc(string $type, bool $nullable): string
{
    $phpType = match($type) {
        'string' => 'string',
        'int' => 'int',
        'float' => 'float',
        'bool' => 'bool',
        'array' => 'array',
        'object' => 'array',
        default => str_contains($type, 'array') ? 'array' : 'mixed',
    };
    
    return $nullable ? "null|$phpType" : $phpType;
}

function mapTypeToValidationRules(string $type, bool $required): string
{
    $rules = [];
    
    if ($required) {
        $rules[] = "'required'";
    } else {
        $rules[] = "'nullable'";
    }
    
    $typeRule = match($type) {
        'string' => "'string'",
        'int' => "'integer'",
        'float' => "'numeric'",
        'bool' => "'boolean'",
        'array' => "'array'",
        'object' => "'array'",
        default => str_contains($type, 'array') ? "'array'" : "'string'",
    };
    
    $rules[] = $typeRule;
    
    return implode(', ', $rules);
}

echo "Form Request generation completed!\n";
